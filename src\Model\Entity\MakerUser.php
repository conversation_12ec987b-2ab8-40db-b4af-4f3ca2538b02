<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Core\Configure;
use Cake\ORM\Entity;

/**
 * メーカーユーザーエンティティ
 * 
 * @property int $id
 * @property int $maker_id
 * @property string $email
 * @property string|null $password
 * @property string|null $last_name
 * @property string|null $first_name
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\MakerUserToken[] $maker_user_tokens
 * @property \App\Model\Entity\Maker $maker
 */
class MakerUser extends Entity
{
    protected $_accessible = [
        'maker_id' => true,
        'email' => true,
        'password' => true,
        'last_name' => true,
        'first_name' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'maker_user_tokens' => true,
        'maker' => true,
    ];

    protected $_hidden = [
        'password',
    ];

    // パスワードのハッシュ化
    protected function _setPassword($password)
    {
        if ($password) {
            return (new DefaultPasswordHasher())->hash($password);
        }
        return null;
    }

    // Kurocoユーザーかどうかを判定
    public function isKurocoUser(): bool
    {
        return $this->password === null;
    }

    // メーカー情報の取得
    public function getMakerName(): ?string
    {
        return $this->maker ? $this->maker->name : null;
    }

    public function getTokensTableName(): string
    {
        return 'MakerUserTokens';
    }

    public function getGroupId(): string
    {
        return Configure::read("Kuroko.api.clientGroupId");
    }
}
