<?php

use App\Config\DotEnvConfig;
use App\Kuroko\Http\Client\Auth\KurokoApiAuthorization;
use App\Kuroko\Http\Client\KurokoApiClient;
use App\Kuroko\Http\Client\KurokoApiMockClient;

$useMock = isUnitTest() || DotEnvConfig::readBoolean("KUROKO_API_USE_MOCK", false);

return [
    "Kuroko" => [
        "api" => [
            //@todo 環境差異するならDotEnvにもっていく、unittestの時に注意
            "userGroupId" => "104",
            "clientGroupId" => "105",
            "swbGroupId" => "106",
            "swbConfirmGroupId" => "108",
            "allListMaxCnt" => 1000000,
            "params" => [
                "schoolBagForm" => [
                    "fromId" => 1,
                    "memberId" => 0,
                ],
                "eCOrder" => [
                    "ecPaymentId" => 58,
                    "quantity" => 1,
                ],
            ],
            "connection" => $useMock ? KurokoApiMockClient::class : KurokoApiClient::class,
            'token' => [
                'privateStatic' => DotEnvConfig::read("KUROKO_API_TOKEN_PRIVATE_STATIC"),
                'static' => DotEnvConfig::read("KUROKO_API_TOKEN_STATIC"),
                'dynamic' => [
                    'sysUser' => DotEnvConfig::read("KUROKO_API_TOKEN_DYNAMIC_SYS_USER"),
                ]
            ],
            "client" => [
                'auth' => [
                    'type' => KurokoApiAuthorization::class,
                ],
                'type' => 'json',
                'host' => DotEnvConfig::read("KUROKO_API_CLIENT_HOST"),
                'port' => null,
                'scheme' => DotEnvConfig::read("KUROKO_API_CLIENT_SCHEME"),
                'basePath' => DotEnvConfig::read("KUROKO_API_CLIENT_BASE_PATH"),
                'timeout' => 600,
                'ssl_verify_peer' => false,
                'ssl_verify_peer_name' => false,
                'ssl_verify_depth' => 5,
                'ssl_verify_host' => false,
                'redirect' => false,

                // 接続先
                "endPoint" => [
                    "static" => [
                        "sendInquiry" => "/3/send-inquiry"
                    ],
                    "privateStatic" => [
                        "products" => "/4/ec/product/list",
                        "productAllList" => "/4/ec/product/all-list",
                        "makers" => "/4/ec/maker/list"
                    ],
                    "dynamic" => [
                        "memberRegist" => "/5/member/regist",
                        "memberRegistValidateOnly" => "/5/member/regist-validate-only",
                        "memberMe" => "/5/member/me",
                        "memberUpdate" => "/5/member/update",
                        "memberList" => "/5/member/list",
                        "memberSwbList" => "/5/member/swb-list",
                        "updateStatusAndGetFormInfo" => "/5/member/update-status-and-get-form-info",
                        "eCOrderPurchase" => "/5/ec/order/purchase",
                        "eCOrderList" => "/5/ec/order/list",
                        "eCOrderAllList" => "/5/ec/order/all-list",
                        "login" => "/5/login",
                        "loginToken" => "/5/login/token",
                        "loginReminder" => "/5/login/reminder",
                        "loginResetPassword" => "/5/login/reset-password",
                        "logout" => "/5/login/logout",
                        "inquiries" => "/5/inquiry/list",
                    ],
                ],
            ],
        ]
    ]
];
