// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack": [
        "../node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/defu"
      ],
      "h3": [
        "../node_modules/h3"
      ],
      "consola": [
        "../node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../node_modules/@unhead/vue"
      ],
      "@vue/runtime-core": [
        "../node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/@vue/compiler-sfc"
      ],
      "@vue/runtime-dom": [
        "../node_modules/@vue/runtime-dom"
      ],
      "@nuxt/schema": [
        "../node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/nuxt"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "assets": [
        "../assets"
      ],
      "assets/*": [
        "../assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "pinia": [
        "../node_modules/pinia/dist/pinia"
      ],
      "#vue-router": [
        "./vue-router-stub"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ],
      "#components": [
        "./components"
      ]
    },
    "forceConsistentCasingInFileNames": true,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "skipLibCheck": true,
    "isolatedModules": true,
    "useDefineForClassFields": true,
    "strict": true,
    "noImplicitThis": true,
    "esModuleInterop": true,
    "types": [],
    "verbatimModuleSyntax": true,
    "allowJs": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "./nuxt.d.ts",
    "../.config/nuxt.*",
    "../**/*",
    "../node_modules/@nuxtjs/device/runtime",
    "../node_modules/@nuxt/eslint/runtime",
    "../node_modules/@pinia/nuxt/runtime",
    "../node_modules/@nuxt/devtools/runtime",
    "../node_modules/@nuxt/telemetry/runtime",
    ".."
  ],
  "exclude": [
    "../node_modules",
    "../../node_modules",
    "../node_modules/nuxt/node_modules",
    "../node_modules/@nuxtjs/device/runtime/server",
    "../node_modules/@nuxt/eslint/runtime/server",
    "../node_modules/@pinia/nuxt/runtime/server",
    "../node_modules/@nuxt/devtools/runtime/server",
    "../node_modules/@nuxt/telemetry/runtime/server",
    "../dist",
    "../.output"
  ]
}