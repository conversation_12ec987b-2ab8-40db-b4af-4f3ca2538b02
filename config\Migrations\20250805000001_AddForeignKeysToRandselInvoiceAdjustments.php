<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddForeignKeysToRandselInvoiceAdjustments extends AbstractMigration
{
    /**
     * Up Method.
     *
     * 外部キー制約を追加します。
     * RANDSEL_INVOICE_ADJUSTMENTS.confirmed_by と created_by を SWB_USERS.id に参照
     * RANDSEL_INVOICE_ADJUSTMENT_HISTORIES.created_by を SWB_USERS.id に参照
     *
     * @return void
     */
    public function up(): void
    {
        // 既存データのクリーンアップ：存在しないSWB_USERSのIDを参照しているレコードを修正

        // 1. RANDSEL_INVOICE_ADJUSTMENTS テーブルのクリーンアップ
        $this->execute("
            UPDATE randsel_invoice_adjustments
            SET confirmed_by = NULL
            WHERE confirmed_by IS NOT NULL
            AND confirmed_by NOT IN (SELECT id FROM swb_users)
        ");

        $this->execute("
            UPDATE randsel_invoice_adjustments
            SET created_by = NULL
            WHERE created_by IS NOT NULL
            AND created_by NOT IN (SELECT id FROM swb_users)
        ");

        // 2. RANDSEL_INVOICE_ADJUSTMENT_HISTORIES テーブルのクリーンアップ
        // created_by は NOT NULL なので、デフォルトのSWBユーザーIDに設定するか、レコードを削除
        // まず、有効なSWBユーザーIDを取得
        $validSwbUserId = $this->fetchRow("SELECT id FROM swb_users ORDER BY id LIMIT 1");

        if ($validSwbUserId) {
            // 有効なSWBユーザーが存在する場合、そのIDに設定
            $this->execute("
                UPDATE randsel_invoice_adjustment_histories
                SET created_by = {$validSwbUserId['id']}
                WHERE created_by IS NOT NULL
                AND created_by NOT IN (SELECT id FROM swb_users)
            ");
        } else {
            // 有効なSWBユーザーが存在しない場合、該当レコードを削除
            $this->execute("
                DELETE FROM randsel_invoice_adjustment_histories
                WHERE created_by IS NOT NULL
                AND created_by NOT IN (SELECT id FROM swb_users)
            ");
        }

        // RANDSEL_INVOICE_ADJUSTMENTS テーブルの外部キー制約を追加
        $table = $this->table('randsel_invoice_adjustments');
        
        // confirmed_by フィールドにインデックスを追加（外部キー制約のため）
        $table->addIndex(['confirmed_by'], ['name' => 'idx_randsel_invoice_adjustments_confirmed_by']);
        
        // created_by フィールドにインデックスを追加（外部キー制約のため）
        $table->addIndex(['created_by'], ['name' => 'idx_randsel_invoice_adjustments_created_by']);
        
        // confirmed_by フィールドの外部キー制約を追加
        $table->addForeignKey(
            'confirmed_by',
            'swb_users',
            'id',
            [
                'delete' => 'SET_NULL',
                'update' => 'NO_ACTION',
                'constraint' => 'fk_randsel_invoice_adjustments_confirmed_by'
            ]
        );
        
        // created_by フィールドの外部キー制約を追加
        $table->addForeignKey(
            'created_by',
            'swb_users',
            'id',
            [
                'delete' => 'SET_NULL',
                'update' => 'NO_ACTION',
                'constraint' => 'fk_randsel_invoice_adjustments_created_by'
            ]
        );
        
        $table->update();
        
        // RANDSEL_INVOICE_ADJUSTMENT_HISTORIES テーブルの外部キー制約を追加
        $historyTable = $this->table('randsel_invoice_adjustment_histories');
        
        // created_by フィールドにインデックスを追加（外部キー制約のため）
        $historyTable->addIndex(['created_by'], ['name' => 'idx_randsel_invoice_adjustment_histories_created_by']);
        
        // created_by フィールドの外部キー制約を追加
        $historyTable->addForeignKey(
            'created_by',
            'swb_users',
            'id',
            [
                'delete' => 'CASCADE',
                'update' => 'NO_ACTION',
                'constraint' => 'fk_randsel_invoice_adjustment_histories_created_by'
            ]
        );
        
        $historyTable->update();
    }

    /**
     * Down Method.
     *
     * 外部キー制約を削除します。
     *
     * @return void
     */
    public function down(): void
    {
        // RANDSEL_INVOICE_ADJUSTMENTS テーブルの外部キー制約を削除
        $table = $this->table('randsel_invoice_adjustments');
        
        // 外部キー制約を削除
        $table->dropForeignKey('confirmed_by');
        $table->dropForeignKey('created_by');
        
        // インデックスを削除
        $table->removeIndex(['confirmed_by']);
        $table->removeIndex(['created_by']);
        
        $table->update();
        
        // RANDSEL_INVOICE_ADJUSTMENT_HISTORIES テーブルの外部キー制約を削除
        $historyTable = $this->table('randsel_invoice_adjustment_histories');
        
        // 外部キー制約を削除
        $historyTable->dropForeignKey('created_by');
        
        // インデックスを削除
        $historyTable->removeIndex(['created_by']);
        
        $historyTable->update();
    }
}
