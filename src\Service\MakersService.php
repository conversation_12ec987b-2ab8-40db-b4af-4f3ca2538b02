<?php

namespace App\Service;

use App\Model\Table\MakersTable;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Log\Log;

class MakersService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
        "defaultModel" => MakersTable::class,
    ];

    public function add(array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): ?array
    {
        Log::info('MakersService: MySQLベースのメーカー取得を使用');
        return $this->getMysqlMakers($data);
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void {}

    /**
     * MySQLからメーカー情報を取得し、指定された形式で返却
     *
     * @param array $data
     * @return array
     */
    private function getMysqlMakers(array $data = []): array
    {
        /** @var MakersTable $makersTable */
        $makersTable = $this->getDefaultModel();

        // アクティブなメーカーのみを取得（論理削除されていないもの）
        $makers = $makersTable->find()
            ->where(['deleted IS' => null])
            ->orderAsc('id')
            ->all();

        $result = [];
        foreach ($makers as $maker) {
            $result[] = [
                'maker_id' => (string)$maker->id,
                'maker_name' => $maker->name,
                'ext' => [
                    'billing_address' => $maker->billing_address,
                    'customer_code' => $maker->customer_code,
                    'customer_name' => $maker->customer_name,
                    'billing_cycle' => $maker->billing_cycle,
                    'contact_name' => $maker->contact_name,
                ]
            ];
        }

        return $result;
    }
}
