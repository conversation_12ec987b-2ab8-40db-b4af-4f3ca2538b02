<?php

namespace App\Service;

use App\Enums\EntityFields\EUserProfile;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\UserDetailUpdateCompletedSender;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;
use Cake\Datasource\EntityInterface;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Utility\Hash;
use Cake\Log\Log;
use BadMethodCallException;
use Exception;

class UserDetailsService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;

    public function initialize(): void
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        Log::info("UserDetailsService::edit called", ['id' => $data['member_id'], 'data' => $data]);
        /** @var Member $member */
        $member = $this->getIdentity();

        try {
            // 一般ユーザーを取得
            $generalUser = $this->generalUsersTable->find()
                ->contain(['UserProfiles'])
                ->where(['GeneralUsers.id' => $data['member_id']])
                ->first();

            if (!$generalUser) {
                throw new Exception("User not found with ID: {$data['member_id']}");
            }

            if (isset($data['email']) && $data['email'] !== $member->getEmail()) {
                // メールアドレスの重複チェック
                $userRegistrationService = new UserRegistrationService();
                if (!$userRegistrationService->isEmailAvailable($data['email'])) {
                    $this->setErrors([[
                        'code' => "invalid",
                        'field' => "email",
                        'message' => 'このメールアドレスは既に登録されています',
                    ]]);
                    return null;
                }
            }

            // トランザクション内でデータベース更新を実行
            $connection = ConnectionManager::get('default');
            $result = $connection->transactional(function() use ($generalUser, $data) {

                // general_usersテーブルのemail更新
                if (isset($data['email'])) {
                    $generalUser = $this->generalUsersTable->patchEntity($generalUser, [
                        'email' => $data['email']
                    ]);

                    if (!$this->generalUsersTable->save($generalUser)) {
                        throw new Exception('Failed to update user email');
                    }
                    Log::info("Updated general_users email", ['user_id' => $data['member_id'], 'email' => $data['email']]);
                }

                // user_profilesテーブルの更新データを準備
                $profileData = $this->mapDataToProfileFields($data);

                if (!empty($profileData)) {
                    $userProfile = $generalUser->user_profile;
                    if (!$userProfile) {
                        // プロフィールが存在しない場合は新規作成
                        $profileData['general_user_id'] = $generalUser->id;
                        $userProfile = $this->userProfilesTable->newEntity($profileData);
                    } else {
                        // 既存プロフィールを更新
                        $userProfile = $this->userProfilesTable->patchEntity($userProfile, $profileData);
                    }

                    if (!$this->userProfilesTable->save($userProfile)) {
                        throw new Exception('Failed to update user profile');
                    }
                    Log::info("Updated user_profiles", ['user_id' => $data['member_id'], 'profile_data' => $profileData]);
                }

                return true;
            });

            // データベース更新成功時のみメール送信
            $this->sendUpdateCompletedEmail($generalUser);

            Log::info("UserDetailsService::edit completed successfully", ['user_id' => $data['member_id']]);

            // 更新されたユーザー情報を返す
            return null;
        } catch (Exception $e) {
            Log::error("UserDetailsService::edit failed", [
                'user_id' => $data['member_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->setErrors([
                '_system' => $e->getMessage(),
            ]);
            return null;
        }
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        return $member;
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    /**
     * $dataパラメータをuser_profilesテーブルのフィールドにマッピング
     */
    private function mapDataToProfileFields(array $data): array
    {
        $profileData = [];

        // フィールドマッピング
        $fieldMapping = [
            'name1' => EUserProfile::LAST_NAME->value,
            'name2' => EUserProfile::FIRST_NAME->value,
            'name1_hurigana' => EUserProfile::LAST_NAME_KANA->value,
            'name2_hurigana' => EUserProfile::FIRST_NAME_KANA->value,
            'zip_code' => EUserProfile::ZIP_CODE->value,
            'tdfk_cd' => EUserProfile::PREFECTURE_CODE->value,
            'address1' => EUserProfile::ADDRESS1->value,
            'address2' => EUserProfile::ADDRESS2->value,
            'address3' => EUserProfile::ADDRESS3->value,
            'tel' => EUserProfile::TEL->value,
            'email_send_ng_flg' => EUserProfile::EMAIL_SEND_NG_FLG->value,
        ];

        foreach ($fieldMapping as $dataKey => $profileField) {
            if (array_key_exists($dataKey, $data)) {
                $profileData[$profileField] = $data[$dataKey];
            }
        }

        return $profileData;
    }

    /**
     * ユーザー詳細更新完了メールを送信
     */
    private function sendUpdateCompletedEmail($generalUser): void
    {
        try {
            $email = $generalUser->email;
            $lastName = $generalUser->user_profile ? $generalUser->user_profile->decrypted_last_name : '';

            AppMailer::sendToUser(new UserDetailUpdateCompletedSender($email, $lastName));

            Log::info("Update completed email sent", [
                'user_id' => $generalUser->id,
                'email' => $email
            ]);
        } catch (Exception $e) {
            Log::error("Failed to send update completed email", [
                'user_id' => $generalUser->id,
                'error' => $e->getMessage()
            ]);
            // メール送信失敗はエラーとして扱わない（ログのみ）
            throw $e;
        }
    }
}
