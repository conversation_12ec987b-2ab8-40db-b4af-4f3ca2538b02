<script setup lang="ts">
import { ClientMyBridge } from "~/src/models/bridge/ClientMyBridge";
import { RANDSEL_APPROVAL_STATUS_LIST } from "~~/src/list";

const props = defineProps<{
    bridge: ClientMyBridge;
}>();
const form = props.bridge.client_order_search_form;

//TODO:検索ステータスの整理 とりあえず固定値
const searchDateTypeList = [
    { title: "注文日", value: "order-date" },
    { title: "承認日（否認日）", value: "status-updated-date" },
];
</script>

<template>
    <div class="main-wrapper">
        <v-card class="main-card">
            <div class="pa-1 pl-2 title-wrapper">
                ■ 条件を指定して注文リストを表示する
            </div>
            <div class="pl-8 product-select-wrapper">
                カタログ：
                <v-col cols="5">
                    <v-select
                        v-model="form.productId"
                        :items="bridge.my_products"
                        item-title="display_name"
                        item-value="id"
                        hide-details="auto"
                        bg-color="white"
                        density="compact"
                        prev-icon="mdi-chevron-left"
                        next-icon="mdi-chevron-right"
                    ></v-select>
                </v-col>
                の注文リストを表示する
            </div>
            <v-row class="ma-1">
                <v-col cols="4" class="ml-4">
                    <div class="status-wrapper">
                        <div class="status-title">ステータス</div>
                        <div
                            class="pl-2 status-checkbox-wrapper d-flex flex-row"
                        >
                            <template
                                v-for="status of RANDSEL_APPROVAL_STATUS_LIST"
                                :key="status.value"
                            >
                                <v-checkbox
                                    v-model="form.searchStatusType"
                                    :label="status.label"
                                    :value="status.value"
                                    hide-details="auto"
                                    density="compact"
                                />
                            </template>
                        </div>
                    </div>
                </v-col>
                <v-col cols="7" class="ml-10">
                    <div class="search-date-wrapper">
                        <div class="search-date-title">日付指定</div>
                        <div class="pl-2 search-date-form-wrapper">
                            <v-row>
                                <v-col cols="3">
                                    <v-select
                                        v-model="form.searchDateType"
                                        :items="searchDateTypeList"
                                        hide-details="auto"
                                        density="compact"
                                    ></v-select>
                                </v-col>
                                <v-col cols="4">
                                    <v-text-field
                                        v-model="form.startDate"
                                        type="date"
                                        hide-details="auto"
                                        density="compact"
                                        class="form-min-width"
                                        :rules="[
                                            bridge.client_order_search_form.isStartDateValid(),
                                        ]"
                                    />
                                </v-col>
                                <v-col class="text-center" cols="1">～</v-col>
                                <v-col cols="4">
                                    <v-text-field
                                        v-model="form.endDate"
                                        type="date"
                                        hide-details="auto"
                                        density="compact"
                                        class="form-min-width"
                                        :rules="[
                                            bridge.client_order_search_form.isEndDateValid(),
                                        ]"
                                /></v-col>
                            </v-row>
                        </div>
                    </div>
                </v-col>
            </v-row>
            <v-row justify="center" class="mt-1 mb-4">
                <v-btn
                    class="search-btn"
                    :disabled="
                        !bridge.client_order_search_form.isDateValid ||
                        !bridge.client_order_search_form.isChanged
                    "
                    elevation="0"
                    @click="bridge.loadOrders()"
                >
                    <v-icon size="large" class="mr-2">mdi-magnify</v-icon>
                    <p class="search-btn-text">指定した条件で表示する</p>
                </v-btn>
            </v-row>
        </v-card>
    </div>
</template>

<style scoped>
.main-wrapper {
    width: 100%;
    min-width: 1030px;
    font-size: small !important;
}
.main-card {
    width: 100%;
    min-width: 1000px;
}
.title-wrapper {
    background-color: #eaeaea;
}
.product-select-wrapper {
    background-color: #dcdcdc;
    display: flex;
    align-items: center;
}
.status-wrapper {
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
.status-title {
    background-color: #eaeaea;
    padding: 3px 10px;
    align-content: center;
    border-right: #dfdfdf solid 2px;
}
.status-checkbox-wrapper {
    padding: 3px 10px;
}
.search-date-wrapper {
    min-width: 646px;
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
.search-date-title {
    background-color: #eaeaea;
    padding: 3px 10px;
    align-content: center;
    border-right: #dfdfdf solid 2px;
}
.search-date-form-wrapper {
    width: 88%;
    padding: 3px 10px;
    display: flex;
}
.form-min-width {
    min-width: 160px;
}
.text-center {
    align-content: center;
    text-align: center;
}
.search-btn {
    background: #ff8b00;
    color: white;
    transition: opacity 0.3s ease;
}
.search-btn:hover {
    opacity: 0.7;
}
.search-btn:disabled {
    background: #ffffff !important;
    color: #a5a5a5 !important;
}
.search-btn:disabled .search-btn-text {
    color: #707070 !important;
}
.search-btn-text {
    font-size: 16px;
    font-weight: bold;
    color: white;
}
</style>
