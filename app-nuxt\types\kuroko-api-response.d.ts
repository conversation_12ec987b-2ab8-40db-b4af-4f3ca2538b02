type TTopic = {
    [key: string]: string | number | undefined | boolean | null;
    topics_id: string;
};
type TExtColumn = {
    ext_slug: string;
    value: string;
};
type TExtColumns = {
    straight: TExtColumn[];
};
type TProductData = {
    // [key: string]: string | number | undefined | boolean | null;
    ext: string;
    contents: string;
    contents_type: number;
};
type TProduct = {
    // [key: string]: string | number | undefined | boolean | null;
    // product_id: number;
    // product_name: string;
    // topics_name: string;
    // product_data: TProductData;

    id: number,
    maker_id: number,
    brand_id: number,
    display_name: string,
    description_html: string,
    note_html: string,
    mask_image_description: string,
    image_url: string,
    pdf_url: string,
    sort_order: number,
    is_display: boolean,
    year: number,
    brand: object,
    maker: object,
    top_budget: object,
    display_type: "paper" | "digital" | "sold_out";
};

type TMakerExt = {
    billing_address: string;
    billing_cycle: number;
    customer_code: string;
    customer_name: string;
};
type TMaker = {
    // [key: string]: string | number | undefined | boolean | null;
    maker_id: number;
    maker_name: string;
    ext: TMakerExt;
};

type TResponseContent = {
    list: TTopic[];
};

type TResponseEcProduct = {
    list: TProduct[];
};

type TResponseProducts = {
    products: TProduct[];
};

type TResponseMakers = {
    makers: TMaker[];
};

type TResponseAuth = {
    access_token: string;
    member: {
        id: number;
        group_ids: string[];
    };
};

type TResponseAuthError = {
    success: false;
    message: string;
    code: string;
};


type TResponseSuccess = {
    success: boolean;
};

// type TMember = {
//     success: boolean;
// };

type TResponseCsvRandselOrdersError = {
    errors: { failed_ids: number[] };
};

type TApiResponse = {
    errors: ValidationError[];
    messages: string[];
    id: string;
};

type TMonthlyInvoice = {
    maker_id: number;
    total_price: number;
    status_modified_year_month: string;
};

type TAdjustmentHistory = {
    id: number;
    invoice_adjustment_id: number;
    action_type: number;
    changes: Record<string, string>;
    created_by: number;
    created_by_name: string;
    created: string;
};

type TMonthlyInvoiceAdjustment = {
    id: number;
    maker_id: number;
    product_id: number;
    billing_year_month: string;
    adjustment_unit_price: number;
    adjustment_quantity: number;
    adjustment_note: string;
    status: number;
    histories: TAdjustmentHistory[];
};

type TResponseMonthlyInvoices = {
    monthly_invoices: TMonthlyInvoice[];
};

type TResponseMonthlyInvoiceAdjustments = {
    randsel_invoice_adjustments: TMonthlyInvoiceAdjustment[];
};
