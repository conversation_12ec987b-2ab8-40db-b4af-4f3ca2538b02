<?php

declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\MigrateKurocoUsersCommand;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\ApiModel\KurokoApiDynamic\Inquiries;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\Inquiry;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;
use App\Model\Table\UserSurveysTable;

use Cake\Console\ConsoleIo;
use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * MigrateKurocoUsersCommand Test Case
 *
 * @uses \App\Command\MigrateKurocoUsersCommand
 */
class MigrateKurocoUsersCommandTest extends TestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserProfiles',
        'app.SwbUsers',
        'app.MakerUsers',
        'app.UserSurveys',
        'app.TemporaryRegistrations',
    ];

    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;
    private UserSurveysTable $userSurveysTable;

    /**
     * setUp method
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->useCommandRunner();
        
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $this->userSurveysTable = TableRegistry::getTableLocator()->get('UserSurveys');
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        parent::tearDown();
        Log::drop('debug');
        Log::drop('error');
    }

    /**
     * コマンドオプションのテスト
     */
    public function testBuildOptionParser(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $parser = $command->buildOptionParser($command->getOptionParser());

        // オプションの存在確認（CakePHP 4.5の方法）
        $options = $parser->options();
        $this->assertArrayHasKey('dry-run', $options);
        $this->assertArrayHasKey('batch-size', $options);
        $this->assertArrayHasKey('user-ids', $options);
        $this->assertArrayHasKey('verbose', $options);
        $this->assertArrayHasKey('skip-existing', $options);
    }

    /**
     * ドライランモードのテスト
     */
    public function testExecuteDryRun(): void
    {
        // コマンド実行
        $this->exec('migrate_kuroco_users --dry-run --user-ids=1,2 --verbose');

        // 結果検証
        $this->assertExitSuccess();
        $this->assertOutputContains('ドライランモードで実行します');
        $this->assertOutputContains('移行結果');
        $this->assertOutputContains('Kurocoユーザーデータ移行バッチが完了しました');

        // データベースに変更がないことを確認
        $userCount = $this->generalUsersTable->find()->count();
        $this->assertEquals(3, $userCount); // フィクスチャの初期データのみ（削除済みを除く）
    }

    /**
     * 正常な移行処理のテスト
     */
    public function testExecuteNormalMigration(): void
    {
        // コマンド実行（実際のAPIを使わずにテスト）
        $this->exec('migrate_kuroco_users --user-ids=100,101 --batch-size=10 --verbose');

        // 結果検証
        $this->assertExitSuccess();
        $this->assertOutputContains('移行結果');
        $this->assertOutputContains('Kurocoユーザーデータ移行バッチが完了しました');
    }

    /**
     * バッチサイズ指定のテスト
     */
    public function testExecuteWithBatchSize(): void
    {
        $this->exec('migrate_kuroco_users --batch-size=5 --user-ids=1,2,3,4,5,6,7,8,9,10 --dry-run');

        $this->assertExitSuccess();
        $this->assertOutputContains('バッチサイズ: 5');
    }

    /**
     * 既存データスキップのテスト
     */
    public function testExecuteSkipExisting(): void
    {
        // 既存ユーザーを作成
        $existingUser = $this->generalUsersTable->newEntity([
            'id' => 999,
            'email' => '<EMAIL>',
            'password' => null,
        ]);
        $this->generalUsersTable->save($existingUser);

        $this->exec('migrate_kuroco_users --user-ids=999 --skip-existing --dry-run --verbose');

        $this->assertExitSuccess();
        $this->assertOutputContains('Kurocoユーザーデータ移行バッチが完了しました');
        // 注意：実際のAPIからデータが取得されないため、スキップメッセージは表示されない
    }

    /**
     * エラーハンドリングのテスト
     */
    public function testExecuteWithErrors(): void
    {
        // 無効なユーザーIDでテスト
        $this->exec('migrate_kuroco_users --user-ids=invalid --dry-run');

        // エラーが適切に処理されることを確認
        $this->assertExitSuccess(); // コマンド自体は成功するが、エラーが記録される
    }

    /**
     * 詳細ログ出力のテスト
     */
    public function testExecuteVerboseMode(): void
    {
        $this->exec('migrate_kuroco_users --user-ids=1,2 --verbose --dry-run');

        $this->assertExitSuccess();
        $this->assertOutputContains('指定されたユーザーID');
        $this->assertOutputContains('取得したユーザー数');
        $this->assertOutputContains('Kurocoユーザーデータ移行バッチが完了しました');
    }

    /**
     * モックMemberオブジェクトの作成
     */
    private function createMockMembers(): array
    {
        $member1Data = [
            'member_id' => 100,
            'email' => '<EMAIL>',
            'name1' => '山田',
            'name2' => '太郎',
            'name1_hurigana' => 'ヤマダ',
            'name2_hurigana' => 'タロウ',
            'tdfk_cd' => '13',
            'address1' => '東京都渋谷区',
            'address2' => '神宮前1-1-1',
            'address3' => 'テストビル101',
            'zip_code' => '150-0001',
            'tel' => '03-1234-5678',
            'email_send_ng_flg' => 0,
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
        ];

        $member2Data = [
            'member_id' => 101,
            'email' => '<EMAIL>',
            'name1' => '佐藤',
            'name2' => '花子',
            'name1_hurigana' => 'サトウ',
            'name2_hurigana' => 'ハナコ',
            'tdfk_cd' => '27',
            'address1' => '大阪府大阪市',
            'address2' => '中央区本町1-1-1',
            'address3' => null,
            'zip_code' => '540-0001',
            'tel' => '06-1234-5678',
            'email_send_ng_flg' => 1,
            'inst_ymdhi' => '2024-01-02T11:00:00+09:00',
            'update_ymdhi' => '2024-01-02T11:00:00+09:00',
        ];

        return [
            new Member($member1Data),
            new Member($member2Data),
        ];
    }

    /**
     * モックMembersAPIの作成
     */
    private function createMockMembersApi(array $mockMembers): MockObject
    {
        $mockApi = $this->createMock(Members::class);
        $mockApi->method('listForMakerByIds')
            ->willReturn($mockMembers);

        return $mockApi;
    }

    /**
     * 日時パース機能のテスト
     */
    public function testParseKurocoDateTime(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('parseKurocoDateTime');
        $method->setAccessible(true);

        // 正常な日時文字列
        $result = $method->invoke($command, '2024-01-01T10:00:00+09:00');
        $this->assertInstanceOf(FrozenTime::class, $result);

        // null値
        $result = $method->invoke($command, null);
        $this->assertNull($result);

        // 空文字列
        $result = $method->invoke($command, '');
        $this->assertNull($result);

        // 無効な日時文字列（現在時刻がデフォルトとして返される）
        $result = $method->invoke($command, 'invalid-date');
        $this->assertInstanceOf(FrozenTime::class, $result);
    }

    /**
     * フィールドマッピング機能のテスト
     */
    public function testMapMemberToProfileData(): void
    {
        $memberData = [
            'member_id' => 100,
            'name1' => '山田',
            'name2' => '太郎',
            'name1_hurigana' => 'ヤマダ',
            'name2_hurigana' => 'タロウ',
            'tdfk_cd' => '13',
            'address1' => '東京都渋谷区',
            'address2' => '神宮前1-1-1',
            'address3' => 'テストビル101',
            'zip_code' => '150-0001',
            'tel' => '03-1234-5678',
            'email_send_ng_flg' => 0,
        ];

        $member = new Member($memberData);
        $command = new MigrateKurocoUsersCommand();
        
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapMemberToProfileData');
        $method->setAccessible(true);

        $result = $method->invoke($command, $member);

        // マッピング結果の検証
        $this->assertEquals('山田', $result['last_name']);
        $this->assertEquals('太郎', $result['first_name']);
        $this->assertEquals('ヤマダ', $result['last_name_kana']);
        $this->assertEquals('タロウ', $result['first_name_kana']);
        $this->assertEquals('13', $result['prefecture_code']);
        $this->assertEquals('東京都渋谷区', $result['address1']);
        $this->assertEquals('神宮前1-1-1', $result['address2']);
        $this->assertEquals('テストビル101', $result['address3']);
        $this->assertEquals('150-0001', $result['zip_code']);
        $this->assertEquals('03-1234-5678', $result['tel']);
        $this->assertEquals(0, $result['email_send_ng_flg']);
    }

    /**
     * デフォルト値設定のテスト
     */
    public function testSetDefaultProfileValues(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('setDefaultProfileValues');
        $method->setAccessible(true);

        $profileData = [
            'last_name' => '山田',
            // 他のフィールドは未設定
        ];

        $method->invokeArgs($command, [&$profileData]);

        // デフォルト値が設定されることを確認
        $this->assertEquals('山田', $profileData['last_name']);
        $this->assertEquals('', $profileData['first_name']);
        $this->assertEquals('', $profileData['last_name_kana']);
        $this->assertEquals('', $profileData['first_name_kana']);
        $this->assertEquals('', $profileData['zip_code']);
        $this->assertEquals('', $profileData['prefecture_code']);
        $this->assertEquals('', $profileData['address1']);
        $this->assertEquals('', $profileData['tel']);
        $this->assertEquals(false, $profileData['email_send_ng_flg']);
        $this->assertNull($profileData['address2']);
        $this->assertNull($profileData['address3']);
        $this->assertNull($profileData['notes']);
    }

    /**
     * 既存ユーザー存在チェックのテスト
     */
    public function testIsUserExists(): void
    {
        // テスト用ユーザーを作成
        $testUser = $this->generalUsersTable->newEntity([
            'id' => 999,
            'email' => '<EMAIL>',
            'password' => null,
        ]);
        $this->generalUsersTable->save($testUser);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('isUserExists');
        $method->setAccessible(true);

        // 初期化メソッドを呼び出し
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        // 存在するユーザーのテスト
        $result = $method->invoke($command, '999', '<EMAIL>');
        $this->assertTrue($result);

        // 存在しないユーザーのテスト
        $result = $method->invoke($command, '1000', '<EMAIL>');
        $this->assertFalse($result);

        // IDは存在しないがメールアドレスが存在するケース
        $result = $method->invoke($command, '1000', '<EMAIL>');
        $this->assertTrue($result);
    }

    /**
     * general_usersテーブル作成/更新のテスト
     */
    public function testCreateOrUpdateGeneralUser(): void
    {
        $memberData = [
            'member_id' => 200,
            'email' => '<EMAIL>',
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        $method = $reflection->getMethod('createOrUpdateGeneralUser');
        $method->setAccessible(true);

        // 新規ユーザー作成のテスト
        $result = $method->invoke($command, $member);
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertNull($result->password); // Kurocoユーザーの識別用

        // 既存ユーザー更新のテスト
        $updatedMemberData = array_merge($memberData, ['email' => '<EMAIL>']);
        $updatedMember = new Member($updatedMemberData);
        $result = $method->invoke($command, $updatedMember);
        $this->assertEquals('<EMAIL>', $result->email);
    }

    /**
     * user_profilesテーブル作成/更新のテスト
     */
    public function testCreateOrUpdateUserProfile(): void
    {
        // general_userを先に作成
        $generalUser = $this->generalUsersTable->newEntity([
            'id' => 300,
            'email' => '<EMAIL>',
            'password' => null,
        ]);
        $generalUser = $this->generalUsersTable->save($generalUser);

        $memberData = [
            'member_id' => 300,
            'name1' => '田中',
            'name2' => '次郎',
            'name1_hurigana' => 'タナカ',
            'name2_hurigana' => 'ジロウ',
            'tdfk_cd' => '14',
            'address1' => '神奈川県横浜市',
            'address2' => '西区みなとみらい1-1-1',
            'zip_code' => '220-0012',
            'tel' => '045-1234-5678',
            'email_send_ng_flg' => 0,
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        $method = $reflection->getMethod('createOrUpdateUserProfile');
        $method->setAccessible(true);

        // プロフィール作成のテスト
        $method->invoke($command, $generalUser, $member);

        // 作成されたプロフィールを確認
        $profile = $this->userProfilesTable->find()
            ->where(['general_user_id' => $generalUser->id])
            ->first();

        $this->assertNotNull($profile);
        $this->assertEquals($generalUser->id, $profile->general_user_id);
        $this->assertEquals('田中', $profile->decrypted_last_name);
        $this->assertEquals('次郎', $profile->decrypted_first_name);
        $this->assertEquals('タナカ', $profile->decrypted_last_name_kana);
        $this->assertEquals('ジロウ', $profile->decrypted_first_name_kana);
    }

    /**
     * 統合テスト：完全な移行プロセス
     */
    public function testFullMigrationProcess(): void
    {
        $memberData = [
            'member_id' => 400,
            'email' => '<EMAIL>',
            'name1' => '完全',
            'name2' => 'テスト',
            'name1_hurigana' => 'カンゼン',
            'name2_hurigana' => 'テスト',
            'tdfk_cd' => '23',
            'address1' => '愛知県名古屋市',
            'address2' => '中区栄1-1-1',
            'zip_code' => '460-0008',
            'tel' => '052-1234-5678',
            'email_send_ng_flg' => 1,
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        $method = $reflection->getMethod('migrateUser');
        $method->setAccessible(true);

        // 移行処理実行
        $io = $this->createMock(ConsoleIo::class);
        $method->invoke($command, $member, $io, true);

        // general_usersテーブルの確認
        $user = $this->generalUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertNull($user->password);

        // user_profilesテーブルの確認
        $profile = $this->userProfilesTable->find()
            ->where(['general_user_id' => $user->id])
            ->first();
        $this->assertNotNull($profile);
        $this->assertEquals('完全', $profile->decrypted_last_name);
        $this->assertEquals('テスト', $profile->decrypted_first_name);
        $this->assertEquals('愛知県名古屋市', $profile->decrypted_address1);
        $this->assertEquals(true, $profile->email_send_ng_flg);
    }

    /**
     * エラーケースのテスト：無効なメールアドレス
     */
    public function testMigrationWithInvalidEmail(): void
    {
        $memberData = [
            'member_id' => 500,
            'email' => '', // 空のメールアドレス
            'name1' => 'エラー',
            'name2' => 'テスト',
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        $method = $reflection->getMethod('processUser');
        $method->setAccessible(true);

        $io = $this->createMock(ConsoleIo::class);

        // スキップされることを確認（例外が発生しない）
        $method->invoke($command, $member, false, true, $io, true);

        // ユーザーが作成されていないことを確認（空のメールアドレスのため）
        $user = $this->generalUsersTable->find()
            ->where(['email' => ''])
            ->first();
        $this->assertNull($user);
    }

    /**
     * アンケートデータマッピングのテスト
     */
    public function testSurveyDataMapping(): void
    {
        // テスト用のInquiryデータを作成
        $inquiryData = [
            'body' => json_encode([
                'questions' => [
                    ['k' => 'お子さまの性別', 'v' => '女の子'],
                    ['k' => 'ご予算', 'v' => '30,000円～50,000円'],
                    ['k' => 'カタログ請求のきっかけ（複数回答可）', 'v' => ['WEB広告', '知人の紹介']],
                    ['k' => '特に重視するポイント（複数回答可）', 'v' => ['デザイン', '価格']]
                ]
            ])
        ];

        $inquiry = new Inquiry($inquiryData);

        // リフレクションを使用してprivateメソッドをテスト
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapInquiryToSurveyData');
        $method->setAccessible(true);

        $result = $method->invoke($command, $inquiry, 1);

        // 期待される結果を検証
        $this->assertEquals(1, $result['general_user_id']);
        $this->assertEquals(2, $result['child_sex']); // 女の子 = 2
        $this->assertEquals(2, $result['budget']); // 30,000円～50,000円 = 2
        $this->assertTrue($result['question_1_1']); // WEB広告
        $this->assertTrue($result['question_1_3']); // 知人の紹介
        $this->assertTrue($result['question_2_3']); // デザイン
        $this->assertTrue($result['question_2_10']); // 価格
    }

    /**
     * 性別マッピングのテスト
     */
    public function testMapChildSex(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapChildSex');
        $method->setAccessible(true);

        $this->assertEquals(1, $method->invoke($command, '男の子'));
        $this->assertEquals(2, $method->invoke($command, '女の子'));
        $this->assertEquals(3, $method->invoke($command, 'その他'));
        $this->assertEquals(3, $method->invoke($command, '答えたくない'));
        $this->assertNull($method->invoke($command, ''));
        $this->assertNull($method->invoke($command, null));
    }

    /**
     * 予算マッピングのテスト
     */
    public function testMapBudget(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapBudget');
        $method->setAccessible(true);

        $this->assertEquals(1, $method->invoke($command, '～30,000円'));
        $this->assertEquals(2, $method->invoke($command, '30,000円～50,000円'));
        $this->assertEquals(3, $method->invoke($command, '50,000円～100,000円'));
        $this->assertEquals(4, $method->invoke($command, '100,000円～'));
        $this->assertEquals(5, $method->invoke($command, '30万円以上'));
        $this->assertNull($method->invoke($command, ''));
        $this->assertNull($method->invoke($command, null));
    }

    /**
     * きっかけマッピングのテスト
     */
    public function testMapTrigger(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapTrigger');
        $method->setAccessible(true);

        $surveyData = [
            'question_1_1' => false,
            'question_1_2' => false,
            'question_1_3' => false,
            'question_1_4' => false,
        ];
        
        $method->invokeArgs($command, ['WEB広告', &$surveyData]);
        $this->assertTrue($surveyData['question_1_1']);
        
        $method->invokeArgs($command, ['SNS', &$surveyData]);
        $this->assertTrue($surveyData['question_1_2']);

        $method->invokeArgs($command, ['知人の紹介', &$surveyData]);
        $this->assertTrue($surveyData['question_1_3']);
        
        $method->invokeArgs($command, ['家族の紹介', &$surveyData]);
        $this->assertTrue($surveyData['question_1_4']);
    }

    /**
     * 重視するポイントマッピングのテスト
     */
    public function testMapImportantPoint(): void
    {
        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('mapImportantPoint');
        $method->setAccessible(true);

        $surveyData = [
            'question_2_1' => false, // 耐久性
            'question_2_2' => false, // 色
            'question_2_3' => false, // デザイン
            'question_2_4' => false, // 機能性
            'question_2_5' => false, // キャラクターコラボ
            'question_2_6' => false, // 軽量性
            'question_2_7' => false, // 安全性
            'question_2_8' => false, // ブランド
            'question_2_9' => false, // メーカーの信頼性
            'question_2_10' => false, // 価格
            'question_2_11' => false, // カスタマイズ性
        ];

        $method->invokeArgs($command, ['耐久性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_1']);

        $surveyData['question_2_1'] = false; // リセット
        $method->invokeArgs($command, ['色', &$surveyData]);
        $this->assertTrue($surveyData['question_2_2']);

        $surveyData['question_2_2'] = false; // リセット
        $method->invokeArgs($command, ['デザイン', &$surveyData]);
        $this->assertTrue($surveyData['question_2_3']);

        $surveyData['question_2_3'] = false; // リセット
        $method->invokeArgs($command, ['機能性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_4']);

        $surveyData['question_2_4'] = false; // リセット
        $method->invokeArgs($command, ['キャラクターコラボ', &$surveyData]);
        $this->assertTrue($surveyData['question_2_5']);

        $surveyData['question_2_5'] = false; // リセット
        $method->invokeArgs($command, ['軽量性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_6']);

        $surveyData['question_2_6'] = false; // リセット
        $method->invokeArgs($command, ['安全性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_7']);

        $surveyData['question_2_7'] = false; // リセット
        $method->invokeArgs($command, ['ブランド', &$surveyData]);
        $this->assertTrue($surveyData['question_2_8']);

        $surveyData['question_2_8'] = false; // リセット
        $method->invokeArgs($command, ['メーカーの信頼性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_9']);

        $surveyData['question_2_9'] = false; // リセット
        $method->invokeArgs($command, ['価格', &$surveyData]);
        $this->assertTrue($surveyData['question_2_10']);

        $surveyData['question_2_10'] = false; // リセット
        $method->invokeArgs($command, ['カスタマイズ性', &$surveyData]);
        $this->assertTrue($surveyData['question_2_11']);
    }

    /**
     * トランザクションrollbackのテスト：アンケートデータが見つからない場合
     */
    public function testTransactionRollbackWhenInquiryDataNotFound(): void
    {
        // pre_form_idを持つMemberデータを作成（アンケートデータは存在しない想定）
        $memberData = [
            'member_id' => 600,
            'email' => '<EMAIL>',
            'name1' => 'ロールバック',
            'name2' => 'テスト',
            'name1_hurigana' => 'ロールバック',
            'name2_hurigana' => 'テスト',
            'tdfk_cd' => '13',
            'address1' => '東京都渋谷区',
            'address2' => '神宮前1-1-1',
            'zip_code' => '150-0001',
            'tel' => '03-1234-5678',
            'email_send_ng_flg' => 0,
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
            'pre_form_id' => 99999, // 存在しないpre_form_id
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        // Inquiries APIをモック化（空の配列を返すように設定）
        $mockInquiriesApi = $this->createMock(Inquiries::class);
        $mockInquiriesApi->method('getByPreFormId')
            ->willReturn([]); // 空の配列を返す（アンケートデータが見つからない）

        // モックAPIをコマンドに注入
        $inquiriesApiProperty = $reflection->getProperty('inquiriesApi');
        $inquiriesApiProperty->setAccessible(true);
        $inquiriesApiProperty->setValue($command, $mockInquiriesApi);

        $migrateMethod = $reflection->getMethod('migrateUser');
        $migrateMethod->setAccessible(true);

        $io = $this->createMock(ConsoleIo::class);

        // 移行前のデータベース状態を確認
        $userCountBefore = $this->generalUsersTable->find()->count();
        $profileCountBefore = $this->userProfilesTable->find()->count();
        $surveyCountBefore = $this->userSurveysTable->find()->count();

        // 例外が発生することを確認し、rollbackが正しく動作することを確認
        $exceptionThrown = false;
        $exceptionMessage = '';

        try {
            $migrateMethod->invoke($command, $member, $io, true);
        } catch (\Exception $e) {
            $exceptionThrown = true;
            $exceptionMessage = $e->getMessage();

            // 移行後のデータベース状態を確認（rollbackされているはず）
            $userCountAfter = $this->generalUsersTable->find()->count();
            $profileCountAfter = $this->userProfilesTable->find()->count();
            $surveyCountAfter = $this->userSurveysTable->find()->count();

            // データが追加されていないことを確認（rollbackされた）
            $this->assertEquals($userCountBefore, $userCountAfter);
            $this->assertEquals($profileCountBefore, $profileCountAfter);
            $this->assertEquals($surveyCountBefore, $surveyCountAfter);

            // 特定のユーザーが作成されていないことを確認
            $user = $this->generalUsersTable->find()
                ->where(['email' => '<EMAIL>'])
                ->first();
            $this->assertNull($user);
        }

        // 例外が発生したことを確認
        $this->assertTrue($exceptionThrown, 'Expected exception was not thrown');
        $this->assertStringContainsString('No inquiry data found for pre_form_id: 999', $exceptionMessage);
    }

    /**
     * processUserSurveyメソッドの例外テスト：アンケートデータが見つからない場合
     */
    public function testProcessUserSurveyThrowsExceptionWhenInquiryDataNotFound(): void
    {
        // テスト用のGeneralUserを作成
        $generalUser = $this->generalUsersTable->newEntity([
            'id' => 600,
            'email' => '<EMAIL>',
            'password' => null,
        ]);
        $generalUser = $this->generalUsersTable->save($generalUser);

        // pre_form_idを持つMemberデータを作成
        $memberData = [
            'member_id' => 600,
            'email' => '<EMAIL>',
            'pre_form_id' => 99999, // 存在しないpre_form_id
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        // Inquiries APIをモック化（空の配列を返すように設定）
        $mockInquiriesApi = $this->createMock(Inquiries::class);
        $mockInquiriesApi->method('getByPreFormId')
            ->willReturn([]); // 空の配列を返す（アンケートデータが見つからない）

        // モックAPIをコマンドに注入
        $inquiriesApiProperty = $reflection->getProperty('inquiriesApi');
        $inquiriesApiProperty->setAccessible(true);
        $inquiriesApiProperty->setValue($command, $mockInquiriesApi);

        $processUserSurveyMethod = $reflection->getMethod('processUserSurvey');
        $processUserSurveyMethod->setAccessible(true);

        // デバッグ：pre_form_idが正しく取得できているかを確認
        $preFormId = $member->get('pre_form_id');
        $this->assertEquals(99999, $preFormId, 'pre_form_id should be 99999');

        // 例外が発生することを確認
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No inquiry data found for pre_form_id: 99999');

        $processUserSurveyMethod->invoke($command, $generalUser, $member, true);
    }



    /**
     * processUserでのrollback処理統合テスト
     */
    public function testProcessUserWithRollbackHandling(): void
    {
        // アンケートデータが見つからないMemberデータを作成
        $memberData = [
            'member_id' => 800,
            'email' => '<EMAIL>',
            'name1' => 'プロセス',
            'name2' => 'ロールバック',
            'name1_hurigana' => 'プロセス',
            'name2_hurigana' => 'ロールバック',
            'tdfk_cd' => '13',
            'address1' => '東京都渋谷区',
            'address2' => '神宮前1-1-1',
            'zip_code' => '150-0001',
            'tel' => '03-1234-5678',
            'email_send_ng_flg' => 0,
            'inst_ymdhi' => '2024-01-01T10:00:00+09:00',
            'update_ymdhi' => '2024-01-01T10:00:00+09:00',
            'pre_form_id' => 777,
        ];
        $member = new Member($memberData);

        $command = new MigrateKurocoUsersCommand();
        $reflection = new \ReflectionClass($command);

        // 初期化
        $initMethod = $reflection->getMethod('initializeCommand');
        $initMethod->setAccessible(true);
        $initMethod->invoke($command);

        // Inquiries APIをモック化（空の配列を返す）
        $mockInquiriesApi = $this->createMock(Inquiries::class);
        $mockInquiriesApi->method('getByPreFormId')
            ->willReturn([]);

        // モックAPIをコマンドに注入
        $inquiriesApiProperty = $reflection->getProperty('inquiriesApi');
        $inquiriesApiProperty->setAccessible(true);
        $inquiriesApiProperty->setValue($command, $mockInquiriesApi);

        $processUserMethod = $reflection->getMethod('processUser');
        $processUserMethod->setAccessible(true);

        $io = $this->createMock(ConsoleIo::class);

        // 移行前のデータベース状態を確認
        $userCountBefore = $this->generalUsersTable->find()->count();
        $profileCountBefore = $this->userProfilesTable->find()->count();

        // processUserを実行（例外はprocessUser内でキャッチされる）
        $processUserMethod->invoke($command, $member, false, false, $io, true);

        // 移行後のデータベース状態を確認
        $userCountAfter = $this->generalUsersTable->find()->count();
        $profileCountAfter = $this->userProfilesTable->find()->count();

        // データが追加されていないことを確認（rollbackされた）
        $this->assertEquals($userCountBefore, $userCountAfter);
        $this->assertEquals($profileCountBefore, $profileCountAfter);

        // 特定のユーザーが作成されていないことを確認
        $user = $this->generalUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNull($user);

        // エラーカウントが増加していることを確認
        $errorCountProperty = $reflection->getProperty('errorCount');
        $errorCountProperty->setAccessible(true);
        $errorCount = $errorCountProperty->getValue($command);
        $this->assertEquals(1, $errorCount);
    }
}
