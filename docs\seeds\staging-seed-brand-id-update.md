# StagingSeed brand_id更新実装ドキュメント

## 概要

covermeプロジェクトのStagingSeedファイルに、`randsel_orders`テーブルの`brand_id`カラムを適切な値に更新する機能を実装しました。

## 実装日時

- **実装日**: 2025年8月18日
- **実装者**: Augment Agent (Claude Sonnet 4)
- **対象ファイル**: `config/Seeds/StagingSeed.php`

## 背景

`randsel_orders`テーブルに`brand_id`カラムが追加されましたが、既存データでは値が設定されていない状態でした。シードデータ投入時に、以下の関連データから適切な`brand_id`を推定・設定する必要がありました：

- `products`テーブルの`brand_id`（優先）
- `makers`テーブルから`brands`テーブルを経由した推定（フォールバック）

## 実装内容

### 更新ロジック

#### 1. Product基準の更新（優先度：高）

```sql
UPDATE randsel_orders ro
INNER JOIN products p ON ro.product_id = p.id
SET ro.brand_id = p.brand_id
WHERE ro.product_id IS NOT NULL 
AND p.brand_id IS NOT NULL
AND ro.brand_id IS NULL;
```

**目的**: `product_id`が設定されている注文について、対応する商品の`brand_id`を設定

#### 2. Maker基準の更新（フォールバック）

```sql
UPDATE randsel_orders ro
INNER JOIN brands b ON ro.maker_id = b.maker_id
SET ro.brand_id = b.id
WHERE ro.product_id IS NULL 
AND ro.maker_id IS NOT NULL 
AND b.maker_id IS NOT NULL
AND ro.brand_id IS NULL
AND b.id = (
    SELECT MIN(b2.id) 
    FROM brands b2 
    WHERE b2.maker_id = ro.maker_id 
    AND b2.deleted IS NULL
);
```

**目的**: `product_id`がNULLの注文について、`maker_id`から最小ID のブランドを推定して設定

### 安全性の考慮事項

1. **外部キー制約の保護**
   - `SET FOREIGN_KEY_CHECKS=0/1`による制約チェック制御
   - 更新処理は制約無効化期間中に実行

2. **データ整合性の保証**
   - `INNER JOIN`による関連データの存在確認
   - `IS NOT NULL`条件による安全な更新
   - 論理削除フィールド（`deleted IS NULL`）の考慮

3. **冪等性の確保**
   - `ro.brand_id IS NULL`条件により重複更新を防止
   - 同じ処理を複数回実行しても結果が変わらない

## 実装位置

`config/Seeds/StagingSeed.php`の以下の位置に追加：

```php
// メンバーのランドセル注文を一般ユーザーに紐づける
$this->execute('UPDATE randsel_orders
    SET general_user_id = member_id
    WHERE member_id IS NOT NULL;
');

// ↓ ここに brand_id 更新処理を追加 ↓

// Re-enable foreign key checks
$this->execute('SET FOREIGN_KEY_CHECKS=1');
```

## テスト結果

- **実行時間**: 2.0092秒
- **実行結果**: 正常完了
- **エラー**: なし

## 影響範囲

### 対象テーブル
- `randsel_orders`: `brand_id`カラムの更新

### 関連テーブル
- `products`: `brand_id`の参照元
- `brands`: 外部キー制約の参照先、maker基準更新の参照元
- `makers`: maker基準更新の間接参照元

### 外部キー制約
- `fk_randsel_orders_brand_id`: `randsel_orders.brand_id` → `brands.id`

## パフォーマンス考慮事項

1. **インデックス活用**
   - `products.id`（主キー）
   - `brands.maker_id`（インデックス）
   - `randsel_orders.product_id`（インデックス）

2. **実行順序**
   - Product基準更新を先に実行（より正確な関連付け）
   - Maker基準更新を後に実行（フォールバック）

## 運用上の注意事項

1. **実行前の確認**
   - データベースのバックアップ実施
   - ステージング環境での事前テスト

2. **実行後の確認**
   - `brand_id`の設定状況確認
   - 外部キー制約違反の有無確認
   - データ整合性の検証

3. **ロールバック方法**
   - 必要に応じて`brand_id`をNULLに戻す
   - バックアップからの復元

## 関連ドキュメント

- [ER図](../_document/ER.md)
- [マイグレーション履歴](../config/Migrations/README.md)
- [テスト仕様書](../tests/TestCase/Seeds/StagingSeedTest.php)

## 変更履歴

| 日付 | 変更者 | 変更内容 |
|------|--------|----------|
| 2025-08-18 | Augment Agent | 初回実装 |
