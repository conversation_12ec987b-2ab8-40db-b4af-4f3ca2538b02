# covermeプロジェクト ドキュメント

## 概要

covermeプロジェクトの技術ドキュメント、運用手順書、実装記録を管理するディレクトリです。

## ディレクトリ構成

```
docs/
├── README.md                                    # このファイル
├── database-optimization-recommendations.md     # データベース最適化推奨事項
├── operational-improvements.md                  # 運用改善提案
├── security-enhancements.md                    # セキュリティ強化提案
├── operations/                                  # 運用手順書
│   └── seed-execution-procedures.md            # シードファイル実行運用手順書
└── seeds/                                       # シードファイル関連ドキュメント
    └── staging-seed-brand-id-update.md         # StagingSeed brand_id更新実装記録
```

## ドキュメント一覧

### 運用関連

#### [シードファイル実行運用手順書](./operations/seed-execution-procedures.md)
- シードファイルの実行手順
- 実行前チェックリスト
- トラブルシューティング
- ロールバック手順

### 実装記録

#### [StagingSeed brand_id更新実装記録](./seeds/staging-seed-brand-id-update.md)
- `randsel_orders`テーブルの`brand_id`更新実装
- 更新ロジックの詳細
- 安全性考慮事項
- テスト結果

### システム改善提案

#### [データベース最適化推奨事項](./database-optimization-recommendations.md)
- インデックス最適化
- クエリパフォーマンス改善
- ストレージ効率化

#### [運用改善提案](./operational-improvements.md)
- 運用プロセスの改善案
- 自動化提案
- 監視強化

#### [セキュリティ強化提案](./security-enhancements.md)
- セキュリティ脆弱性対策
- 認証・認可強化
- データ保護強化

## ドキュメント作成・更新ガイドライン

### 作成原則

1. **明確性**: 技術的な内容を分かりやすく記述
2. **完全性**: 必要な情報を漏れなく記載
3. **追跡可能性**: 変更履歴と担当者を明記
4. **実用性**: 実際の運用で使える内容

### ファイル命名規則

- 英語のケバブケース（例: `seed-execution-procedures.md`）
- 内容が分かりやすい名前
- 日付を含む場合は YYYY-MM-DD 形式

### 必須項目

すべてのドキュメントには以下を含める：

- **概要**: ドキュメントの目的と対象
- **実装日時**: 作成・更新日時
- **担当者**: 作成・更新者
- **変更履歴**: 日付、担当者、変更内容

### マークダウン記法

- 見出しは `#` を使用（H1は1つのみ）
- コードブロックは言語指定付きで記述
- テーブルは適切にフォーマット
- リンクは相対パスを使用

## 関連リソース

### プロジェクト内ドキュメント

- [ER図](../_document/ER.md)
- [マイグレーション履歴](../config/Migrations/README.md)
- [テスト仕様](../tests/)

### 外部リソース

- [CakePHP 4.5 公式ドキュメント](https://book.cakephp.org/4/ja/index.html)
- [PHPUnit ドキュメント](https://phpunit.de/documentation.html)
- [Docker Compose リファレンス](https://docs.docker.com/compose/)

## 更新履歴

| 日付 | 担当者 | 変更内容 |
|------|--------|----------|
| 2025-08-18 | Augment Agent | 初版作成、シード関連ドキュメント追加 |

## 連絡先

ドキュメントに関する質問や更新要望は、プロジェクトの開発チームまでお問い合わせください。
