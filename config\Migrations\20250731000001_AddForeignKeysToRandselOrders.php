<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddForeignKeysToRandselOrders extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {

        $this->table('randsel_orders')
        ->changeColumn('maker_id', 'integer', [
            'comment' => 'メーカーID',
            'default' => null,
            'limit' => null,
            'null' => true,
        ])
        ->changeColumn('product_id', 'integer', [
            'comment' => '注文商品ID',
            'default' => null,
            'limit' => null,
            'null' => true,
        ])
        ->update();

        // maker_idとproduct_idの外部キー制約を別々に追加
        // まずmaker_idのNULL値を処理してから外部キー制約を追加
        $this->execute('UPDATE randsel_orders SET maker_id = NULL WHERE maker_id = 0 OR maker_id NOT IN (SELECT id FROM makers)');
        
        $this->table('randsel_orders')
            ->addForeignKey(
                'maker_id',
                'makers',
                'id',
                [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_randsel_orders_maker_id'
                ]
            )
            ->update();

        // product_idのNULL値を処理してから外部キー制約を追加
        $this->execute('UPDATE randsel_orders SET product_id = NULL WHERE product_id = 0 OR product_id NOT IN (SELECT id FROM products)');
        
        $this->table('randsel_orders')
            ->addForeignKey(
                'product_id',
                'products',
                'id',
                [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_randsel_orders_product_id'
                ]
            )
            ->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $table = $this->table('randsel_orders');
        $table
            ->dropForeignKey('maker_id')
            ->dropForeignKey('product_id')
            ->update();

        $table->update();
    }
}
