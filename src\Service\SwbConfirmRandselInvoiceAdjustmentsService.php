<?php

namespace App\Service;

use Cake\Log\Log;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use App\Model\Table\RandselInvoiceAdjustmentsTable;
use App\Enums\EntityFields\ESwbAdjustmentForm;
use App\Enums\EntityFields\ESwbInvoiceForm;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToSwb\SwbInvoiceAdjustmentConfirmSender;
use App\Config\DotEnvConfig;
use Cake\Core\Configure;
use BadMethodCallException;
use Exception;

class SwbConfirmRandselInvoiceAdjustmentsService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselInvoiceAdjustmentsTable::class,
    ];

    private RandselInvoiceAdjustmentsService $adjustmentsService;
    private RandselInvoicesService $invoiceService;

    public function initialize(): void {}

    public function edit(string $id, array $confirmdata = []): EntityInterface|null
    {
        $this->initializeServices();
        $errors = [];
        $self = $this;

        try {
            $success = $this->getDefaultModel()->getConnection()->transactional(
                fn () => $this->processConfirmations($confirmdata, $errors, $self)
            );

            if ($success) {
                $this->sendConfirmationEmail($confirmdata);
            } else {
                $this->setErrors([
                    '_error' => "承認に失敗しました。",
                ]);
            }
        } catch (Exception $e) {
            $this->setErrors([
                '_error' => "承認に失敗しました。",
                'exception' => $e->getMessage(),
            ]);
        }

        return null;
    }

    private function initializeServices(): void
    {
        $this->adjustmentsService = (new RandselInvoiceAdjustmentsService())->setIdentity($this->getIdentity());
        $this->invoiceService = (new RandselInvoicesService())->setIdentity($this->getIdentity());
    }

    private function processConfirmations(array $confirmdata, array &$errors, $self): bool
    {
        foreach ($confirmdata as $confirmDatum) {
            $adjustment = $this->processAdjustment($confirmDatum, $errors);
            if (!$adjustment) {
                continue;
            }
            $this->processInvoice($confirmDatum, $adjustment, $errors);
        }
        $self->setErrors($errors);
        return empty($errors);
    }

    private function processAdjustment(array $confirmDatum, array &$errors): ?EntityInterface
    {
        $entity = $this->adjustmentsService->getAdjustment([
            ESwbAdjustmentForm::ID->value => Hash::get($confirmDatum, ESwbAdjustmentForm::ID->value),
        ]);

        if (!$entity) {
            $errors[] = "データが見つかりません。";
            return null;
        }

        try {
            if ($adjustment = $this->adjustmentsService->confirmRandselInvoiceAdjustmentData($entity)) {
                return $adjustment;
            }
            $errors[] = "承認に失敗しました。";
            return null;
        } catch (Exception $e) {
            $errors[] = "承認に失敗しました。";
            return null;
        }
    }

    private function processInvoice(array $confirmDatum, EntityInterface $adjustment, array &$errors): void
    {
        $invoice = $this->getOrCreateInvoice($confirmDatum, $adjustment, $errors);
        if ($invoice) {
            $this->updateInvoice($invoice, $adjustment);
        }
    }

    private function getOrCreateInvoice(array $confirmDatum, EntityInterface $adjustment, array &$errors): ?EntityInterface
    {
        $invoice = $this->invoiceService->getInvoice($this->buildInvoiceConditions($confirmDatum));
        if (!$invoice) {
            return $this->createNewInvoice($confirmDatum, $adjustment, $errors);
        }
        return $invoice;
    }

    private function buildInvoiceConditions(array $confirmDatum): array
    {
        return [
            ESwbInvoiceForm::MAKER_ID->value => Hash::get($confirmDatum, ESwbInvoiceForm::MAKER_ID->value),
            ESwbInvoiceForm::PRODUCT_ID->value => Hash::get($confirmDatum, ESwbInvoiceForm::PRODUCT_ID->value),
            ESwbInvoiceForm::BILLING_YEAR_MONTH->value => Hash::get($confirmDatum, ESwbInvoiceForm::BILLING_YEAR_MONTH->value),
        ];
    }

    private function createNewInvoice(array $confirmDatum, EntityInterface $adjustment, array &$errors): ?EntityInterface
    {
        $newInvoiceData = [
            ESwbInvoiceForm::MAKER_ID->value => Hash::get($confirmDatum, ESwbInvoiceForm::MAKER_ID->value),
            ESwbInvoiceForm::PRODUCT_ID->value => Hash::get($confirmDatum, ESwbInvoiceForm::PRODUCT_ID->value),
            ESwbInvoiceForm::BILLING_YEAR_MONTH->value => Hash::get($confirmDatum, ESwbInvoiceForm::BILLING_YEAR_MONTH->value),
            ESwbInvoiceForm::TOTAL_AMOUNT->value => 0,
            ESwbInvoiceForm::ADJUSTMENT_AMOUNT->value => $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE->value) * $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value),
            ESwbInvoiceForm::ADJUSTMENT_QUANTITY->value => $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value),
            ESwbInvoiceForm::ADJUSTMENT_NOTE->value => $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_NOTE->value),
            ESwbInvoiceForm::INVOICE_AMOUNT->value => $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE->value) * $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value),
        ];

        if (!$this->invoiceService->createRandselInvoice($newInvoiceData)) {
            $errors[] = "請求データの新規作成に失敗しました。";
            return null;
        }
        return null;
    }

    private function updateInvoice(EntityInterface $invoice, EntityInterface $adjustment): void
    {
        $invoice->set(ESwbInvoiceForm::ADJUSTMENT_AMOUNT->value, $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE->value) * $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value));
        $invoice->set(ESwbInvoiceForm::ADJUSTMENT_QUANTITY->value, $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value));
        $invoice->set(ESwbInvoiceForm::ADJUSTMENT_NOTE->value, $adjustment->get(ESwbAdjustmentForm::ADJUSTMENT_NOTE->value));
        $invoice->set(ESwbInvoiceForm::INVOICE_AMOUNT->value, $invoice->get(ESwbInvoiceForm::TOTAL_AMOUNT->value) + $invoice->get(ESwbInvoiceForm::ADJUSTMENT_AMOUNT->value));

        $this->invoiceService->updateRandselInvoice($invoice);
    }

    private function sendConfirmationEmail(array $confirmdata): void
    {
        // メーカー名と商品名の取得
        // $makers = (new MakersService())->index([]);
        // $products = (new AllProductsService())->index([]);

        // $makerList = [];
        // foreach ($makers as $maker) {
        //     $makerList[$maker->getMakerId()] = $maker->getMakerName();
        // }

        // $productList = [];
        // foreach ($products as $product) {
        //     $productList[$product->get("product_id")] = $product->get("product_name");
        // }

        $makerIds = array_unique(Hash::extract($confirmdata, '{n}.maker_id'));
        $productIds = array_unique(Hash::extract($confirmdata, '{n}.product_id'));

        $makerList = $this->getTableLocator()->get('Makers')->find('list')->where(['id IN' => $makerIds])->toArray();
        $productList = $this->getTableLocator()->get('Products')->find('list')->where(['id IN' => $productIds])->toArray();

        // 確認データにメーカー名と商品名を追加
        $confirmdata = array_map(function($item) use ($makerList, $productList) {
            $item = Hash::insert($item, 'maker_name', Hash::get($makerList, $item['maker_id'], ''));
            $item = Hash::insert($item, 'product_name', Hash::get($productList, $item['product_id'], ''));
            return $item;
        }, $confirmdata);

        try {
            AppMailer::sendToSwb(
                (new SwbInvoiceAdjustmentConfirmSender(
                    DotEnvConfig::read('NOTIFICATION_EMAIL'),
                    "担当者様"
                ))->setViewVars([
                    'adjustments' => $confirmdata,
                ])
            );
        } catch (Exception $e) {
            // メール送信失敗のログ記録
            Log::error('確認メール送信失敗: ' . $e->getMessage());
        }
    }

    public function index(array $data = []): array|EntityInterface|null
    {
        throw new BadMethodCallException();
    }

    public function add(array $data = []): EntityInterface|null
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }
} 