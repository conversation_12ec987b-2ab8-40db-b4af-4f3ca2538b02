# シードファイル実行運用手順書

## 概要

covermeプロジェクトにおけるシードファイルの実行手順と運用ガイドラインを定義します。

## 対象シードファイル

- `StagingSeed.php`: ステージング環境用データ
- `ProductionSeed.php`: 本番環境用データ  
- `TestAllSeed.php`: テスト環境用データ

## 実行前チェックリスト

### 必須確認事項

- [ ] データベースバックアップの実施
- [ ] 実行環境の確認（staging/production/test）
- [ ] 関連マイグレーションの適用状況確認
- [ ] 外部キー制約の状態確認
- [ ] ディスク容量の確認

### 推奨確認事項

- [ ] 実行時間帯の調整（業務時間外推奨）
- [ ] 関係者への事前通知
- [ ] ロールバック手順の準備
- [ ] モニタリング体制の確保

## 実行手順

### 1. 環境確認

```bash
# 現在の環境確認
docker-compose ps

# データベース接続確認
docker-compose exec amazonlinux2 php bin/cake.php migrations status
```

### 2. バックアップ実施

```bash
# データベースバックアップ（例）
docker-compose exec mysql mysqldump -u root -proot coverme_local > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 3. シードファイル実行

#### ステージング環境

```bash
docker-compose exec amazonlinux2 php bin/cake.php migrations seed --seed StagingSeed
```

#### 本番環境

```bash
docker-compose exec amazonlinux2 php bin/cake.php migrations seed --seed ProductionSeed
```

#### テスト環境

```bash
docker-compose exec amazonlinux2 php bin/cake.php migrations seed --seed TestAllSeed
```

### 4. 実行結果確認

```bash
# 実行ログの確認
# 正常完了メッセージ: "All Done. Took X.XXXXs"

# エラーがある場合は詳細ログを確認
tail -f logs/error.log
```

## 実行後検証手順

### 1. データ整合性確認

```sql
-- randsel_ordersのbrand_id設定状況確認
SELECT 
    COUNT(*) as total_orders,
    COUNT(brand_id) as orders_with_brand_id,
    ROUND((COUNT(brand_id) / COUNT(*)) * 100, 2) as coverage_percentage
FROM randsel_orders;

-- 外部キー制約違反チェック
SELECT ro.id, ro.brand_id 
FROM randsel_orders ro 
LEFT JOIN brands b ON ro.brand_id = b.id 
WHERE ro.brand_id IS NOT NULL AND b.id IS NULL;
```

### 2. 関連テーブル確認

```sql
-- makers, brands, products データ確認
SELECT COUNT(*) FROM makers;
SELECT COUNT(*) FROM brands;
SELECT COUNT(*) FROM products;

-- ユーザーデータ確認
SELECT COUNT(*) FROM swb_users;
SELECT COUNT(*) FROM maker_users;
```

### 3. アプリケーション動作確認

- [ ] 管理画面ログイン確認
- [ ] 注文一覧表示確認
- [ ] ブランド情報表示確認
- [ ] メーカー情報表示確認

## トラブルシューティング

### よくある問題と対処法

#### 1. 外部キー制約エラー

**症状**: `Cannot add or update a child row: a foreign key constraint fails`

**対処法**:
```sql
-- 制約確認
SHOW CREATE TABLE randsel_orders;

-- 問題データ特定
SELECT * FROM randsel_orders WHERE brand_id NOT IN (SELECT id FROM brands);

-- データ修正または制約の一時無効化
SET FOREIGN_KEY_CHECKS=0;
-- 修正処理
SET FOREIGN_KEY_CHECKS=1;
```

#### 2. 実行時間超過

**症状**: シード実行が長時間完了しない

**対処法**:
- バッチサイズの調整
- インデックスの確認
- 不要なデータの事前削除

#### 3. メモリ不足エラー

**症状**: `Fatal error: Allowed memory size exhausted`

**対処法**:
```bash
# PHP メモリ制限の一時的な増加
docker-compose exec amazonlinux2 php -d memory_limit=512M bin/cake.php migrations seed --seed StagingSeed
```

## ロールバック手順

### 1. データベース復元

```bash
# バックアップからの復元
docker-compose exec -i mysql mysql -u root -proot coverme_local < backup_YYYYMMDD_HHMMSS.sql
```

### 2. 部分的なロールバック

```sql
-- 特定テーブルのデータクリア（例）
TRUNCATE TABLE randsel_orders;
TRUNCATE TABLE products;
TRUNCATE TABLE brands;
TRUNCATE TABLE makers;
```

## モニタリング項目

### 実行中監視

- CPU使用率
- メモリ使用率
- ディスクI/O
- データベース接続数

### 実行後監視

- アプリケーションレスポンス時間
- エラーログ出力状況
- データベースパフォーマンス
- ユーザー影響の有無

## 緊急時連絡先

| 役割 | 担当者 | 連絡先 |
|------|--------|--------|
| システム管理者 | - | - |
| データベース管理者 | - | - |
| 開発責任者 | - | - |

## 関連ドキュメント

- [StagingSeed brand_id更新実装ドキュメント](./staging-seed-brand-id-update.md)
- [マイグレーション実行手順](../config/Migrations/README.md)
- [データベース設計書](../_document/ER.md)

## 変更履歴

| 日付 | 変更者 | 変更内容 |
|------|--------|----------|
| 2025-08-18 | Augment Agent | 初版作成、brand_id更新対応追加 |
