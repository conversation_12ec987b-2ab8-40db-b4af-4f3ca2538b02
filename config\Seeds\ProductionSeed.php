<?php
declare(strict_types=1);

use Migrations\AbstractSeed;
use Authentication\PasswordHasher\DefaultPasswordHasher;

/**
 * Production seed.
 * Production環境用のシードデータ
 */
class ProductionSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * https://book.cakephp.org/migrations/3/en/index.html#creating-seeds
     *
     * @return void
     */
    public function run(): void
    {
        // Disable foreign key checks
        $this->execute('SET FOREIGN_KEY_CHECKS=0');

        // Truncate tables
        $this->table('budgets')->truncate();
        $this->table('products')->truncate();
        $this->table('brands')->truncate();
        $this->table('makers')->truncate();
        $this->table('swb_users')->truncate();
        $this->table('maker_users')->truncate();

        // Insert data for makers
        $makersData = [
            [
                'id' => 17,
                'name' => '羅羅屋',
                'description' => 'ララちゃんランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/rara_logo_200.jpg',
                'billing_address' => "〒334-0013
埼玉県川口市南鳩ヶ谷3-22-1
株式会社羅羅屋
執行役員総務部長 松本信和 様",
                'customer_code' => 'T2777',
                'customer_name' => '株式会社羅羅屋',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 18,
                'name' => '羽倉',
                'description' => '羽倉ランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/tmp/cm_hakura.jpg',
                'billing_address' => "〒564-0062
大阪府吹田市垂水町3丁目7-18
株式会社アーツ
マーケティング部門 金城 由起子 様",
                'customer_code' => 'T4312',
                'customer_name' => '株式会社アーツ',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 19,
                'name' => '萬勇鞄',
                'description' => '萬勇鞄のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'billing_address' => "〒490-1104
愛知県あま市西今宿平割一40番地1
萬勇鞄株式会社
服部 達昭 様",
                'customer_code' => 'T3123',
                'customer_name' => '萬勇鞄株式会社',
                'billing_cycle' => 1,
                'contact_name' => '石黒',
            ],
            [
                'id' => 20,
                'name' => 'カバンのフジタ',
                'description' => 'フジタのランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/tmp/fujita_200x200.png',
                'billing_address' => "〒990-0031
山形県山形市十日町1-2-27
株式会社カバンのフジタ
代表取締役 藤田 宏基 様",
                'customer_code' => 'T3860',
                'customer_name' => '株式会社カバンのフジタ',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 21,
                'name' => '鞄工房山本',
                'description' => '鞄工房山本のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'billing_address' => "〒634-0022
奈良県橿原市南浦町899
株式会社鞄工房山本
マーケティングチーム 橋爪 公美 様",
                'customer_code' => 'T2797',
                'customer_name' => '株式会社鞄工房山本',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 22,
                'name' => '東玉',
                'description' => '戸塚ランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'billing_address' => "〒339-0057
さいたま市岩槻区本町3-2-32 東玉人形ビル
株式会社東玉
本店営業部 係長 近藤 隆裕 様",
                'customer_code' => 'T5125',
                'customer_name' => '株式会社東玉',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 23,
                'name' => '榮伸',
                'description' => '榮伸のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/conosaki_0107-square-2_200.png',
                'billing_address' => "〒103-0006
東京都中央区日本橋富沢町8番6号ORCHID PLACE
株式会社榮伸
営業本部 D2C事業部 鵜木 圭一 様",
                'customer_code' => 'T5270',
                'customer_name' => '株式会社榮伸',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 24,
                'name' => '協和',
                'description' => '協和のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/kyowa_200.jpg',
                'billing_address' => "〒277-0835
東京都千代田区東神田2-10-14日本センヂミアビル9F
株式会社協和
営業2部2課 課長代理 郡山 裕弥 様",
                'customer_code' => 'T5267',
                'customer_name' => '株式会社協和',
                'billing_cycle' => 2,
                'contact_name' => '石黒',
            ],
            [
                'id' => 25,
                'name' => 'キシル',
                'description' => 'キシルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/atara_soldout_03_200.png',
                'billing_address' => "〒432-8021
静岡県浜松市中央区佐鳴台5-30-21
株式会社キシル
高崎 麻衣 様",
                'customer_code' => 'T5268',
                'customer_name' => '株式会社キシル',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 26,
                'name' => '盛田',
                'description' => '盛田のランドセルメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'billing_address' => "〒031-0032
青森県八戸市三日町14-1
株式会社盛田
太田 彩子 様",
                'customer_code' => 'T5071',
                'customer_name' => '株式会社盛田',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 27,
                'name' => 'EISHIN（榮伸）',
                'description' => 'EISHIN（榮伸）のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/EISHIN_soldout_200.png',
                'billing_address' => "〒103-0006
東京都中央区日本橋富沢町8番6号ORCHID PLACE
株式会社榮伸
営業本部 D2C事業部 鵜木 圭一 様",
                'customer_code' => 'T5270',
                'customer_name' => '株式会社榮伸',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 28,
                'name' => '山耕株式会社',
                'description' => '恐竜ランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/yamakou_200-1.png',
                'billing_address' => "〒915-0816
福井県越前市小松２丁目23-20
山耕株式会社
代表取締役　山田耕一郎 様",
                'customer_code' => 'T5274',
                'customer_name' => '山耕株式会社',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 29,
                'name' => 'コクホー株式会社',
                'description' => 'コクホーのランドセルメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/kokuho_200.jpg',
                'billing_address' => "〒540-0012
大阪府大阪市中央区谷町2丁目3番4号
サンシャイン大手町ビル501
コクホー株式会社
総務部　峰松　洋様",
                'customer_code' => 'T5277',
                'customer_name' => 'コクホー株式会社',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 30,
                'name' => '株式会社後藤重',
                'description' => 'ごとうじゅうランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/04/gotojyu_teisyi_200.png',
                'billing_address' => "〒452-0942
愛知県清須市清州2625番地
株式会社後藤重
常務取締役　後藤祐輝様",
                'customer_code' => 'T5276',
                'customer_name' => '株式会社後藤重',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
            [
                'id' => 31,
                'name' => 'CHIKYU株式会社',
                'description' => '地球NASAランドセル®のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_soldout_03_200.png',
                'billing_address' => "〒550-0013
大阪府大阪市西区新町4-1-4-9F
CHIKYU 株式会社
大阪営業所マネージャー　桝田　雄一様",
                'customer_code' => 'T5282',
                'customer_name' => 'CHIKYU 株式会社',
                'billing_cycle' => 0,
                'contact_name' => '石黒',
            ],
        ];
        $this->table('makers')->insert($makersData)->save();

        // Insert data for brands
        $brandsData = [
            [
                'id' => 1,
                'maker_id' => 17,
                'name' => 'ララちゃんランドセル',
                'description' => 'ララちゃんランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/10/rara_logo_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 2,
                'maker_id' => 18,
                'name' => '羽倉ランドセル',
                'description' => '羽倉ランドセルブランド',
                'logo_url' => 'https://coverme.jp/tmp/cm_hakura.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 3,
                'maker_id' => 19,
                'name' => '萬勇鞄',
                'description' => '萬勇鞄ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'is_premium' => false,
            ],
            [
                'id' => 4,
                'maker_id' => 20,
                'name' => 'フジタのランドセル',
                'description' => 'フジタのランドセルブランド',
                'logo_url' => 'https://coverme.jp/tmp/fujita_200x200.png',
                'is_premium' => false,
            ],
            [
                'id' => 5,
                'maker_id' => 21,
                'name' => '鞄工房山本',
                'description' => '鞄工房山本ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 6,
                'maker_id' => 22,
                'name' => '戸塚ランドセル',
                'description' => '戸塚ランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'is_premium' => false,
            ],
            [
                'id' => 7,
                'maker_id' => 23,
                'name' => 'conosaki',
                'description' => 'conosakiブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/01/conosaki_0107-square-2_200.png',
                'is_premium' => false,
            ],
            [
                'id' => 8,
                'maker_id' => 24,
                'name' => 'ふわりぃランドセル',
                'description' => 'ふわりぃランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/12/kyowa_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 9,
                'maker_id' => 25,
                'name' => 'アタラのランドセル',
                'description' => 'アタラのランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/03/atara_soldout_03_200.png',
                'is_premium' => false,
            ],
            [
                'id' => 10,
                'maker_id' => 26,
                'name' => '盛田のランドセル',
                'description' => '盛田のランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 11,
                'maker_id' => 27,
                'name' => 'EISHIN',
                'description' => 'EISHINブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/02/EISHIN_soldout_200.png',
                'is_premium' => false,
            ],
            [
                'id' => 12,
                'maker_id' => 28,
                'name' => '恐竜ランドセル',
                'description' => '恐竜ランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/01/yamakou_200-1.png',
                'is_premium' => false,
            ],
            [
                'id' => 13,
                'maker_id' => 29,
                'name' => 'コクホーのランドセル',
                'description' => 'コクホーのランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/02/kokuho_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 14,
                'maker_id' => 30,
                'name' => 'ごとうじゅうランドセル',
                'description' => 'ごとうじゅうランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/04/gotojyu_teisyi_200.png',
                'is_premium' => false,
            ],
            [
                'id' => 15,
                'maker_id' => 31,
                'name' => '地球NASAランドセル®',
                'description' => '地球NASAランドセル®ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_soldout_03_200.png',
                'is_premium' => false,
            ],
        ];
        $this->table('brands')->insert($brandsData)->save();

        // Insert data for products (staging環境用に主要な商品のみ)
        $productsData = [
            [
                'id' => 41211,
                'maker_id' => 17,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '☆ララちゃんランドセル☆羅羅屋（2026総合カタログ）',
                'description_html' => '☆ララちゃんランドセルは、自社工場で心を込めて手作りでお届けします。業界初のオーダーメイド。今では200億通り以上の組み合わせで自分だけのランドセルをつくれます。小さな身体でも背負いやすい負担軽減『マジかるベルト』や、お洒落なデザインにも注目。
【安心】６年間完全無料修理保証スタート！貸出しサービスも大好評。',
                'note_html' => '2025年1月下旬～順次発送予定',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/rara_250114_200_2.png',
                'sort_order' => 100,
            ],
            [
                'id' => 41210,
                'maker_id' => 18,
                'brand_id' => 2,
                'is_display' => false,
                'year' => 2026,
                'display_name' => '羽倉ランドセル（2026年総合カタログ）',
                'description_html' => '【初の豊岡鞄認定ランドセル】
カラーで選ぶなら「羽倉」。世界に一つ、自分だけのオーダーランドセルも作れます。
６年間の途中で、色の好みが変わっても安心！「フラップ交換サービス」も実施中。',
                'note_html' => '2024年12月下旬～順次発送予定',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/hakura_square.jpg',
                'sort_order' => 99,
            ],
            [
                'id' => 41208,
                'maker_id' => 19,
                'brand_id' => 3,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '萬勇鞄（2026年度カタログ）',
                'description_html' => '萬勇鞄は確かな技術で、つくり続ける【名古屋ランドセル】
OnlyONEの個性に応える、手づくりランドセルをコンセプトに一人ひとりの「大好き」や「ワクワク」を叶える、デザインと100超のカラーラインナップ。
ランドセル探しは、お子さまの“感性”と“自分らしさ”が花開く絶好のチャンス。
「自宅でランドセル体験を。」
見て、背負って、体感できる。貸出サービスも行っております。',
                'note_html' => '2025年1月中旬～順次発送予定',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'sort_order' => 98,
            ],
            [
                'id' => 41209,
                'maker_id' => 20,
                'brand_id' => 4,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'フジタのランドセル（2026年度カタログ）',
                'description_html' => '安全もワクワクも！七色に光るランドセル！

本格工房系ランドセル。雪に強く丈夫で、天然皮革の馴染む性質を最大限に活かした、作感性工学に基づいた背負い心地と負担軽減を考慮した数少ない貴重なランドセル。',
                'note_html' => '2025年1月末～順次発送予定',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/fujita_resized_200x200.png',
                'sort_order' => 97,
            ],
            [
                'id' => 41212,
                'maker_id' => 21,
                'brand_id' => 5,
                'is_display' => false,
                'year' => 2026,
                'display_name' => '鞄工房山本（2026年度カタログ）',
                'description_html' => 'ご家族にとって人生に残るランドセルを、奈良の工房からお届けします。
ラインナップは102種、お子さまの個性を表現できる豊富なデザインとカラーを展開。
4店舗ある直営店のほか、全国各地での出張店舗やご自宅へのレンタルランドセルでもランドセルをご試着いただけます。',
                'note_html' => '2024年12月下旬～順次発送予定',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'sort_order' => 96,
            ],
            [
                'id' => 41213,
                'maker_id' => 22,
                'brand_id' => 6,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '戸塚ランドセル（2026年度カタログ）',
                'description_html' => '子どもたちの笑顔やあたたかい日常からインスピレーションを得た優しいデザインのランドセル。
工房系ランドセルとしては最大級の収納力「Grand Cube‐グランドキューブ」が特長。ロック錠は自動回転機能にとどまらず、子供たちの成長に合わせて身幅の調整が可能なスライド機能も搭載。6年間ずっと使える機能性とデザイン性を兼ね備えたランドセルです。',
                'note_html' => '2025年1月10日～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'sort_order' => 95,
            ],
            [
                'id' => 41214,
                'maker_id' => 23,
                'brand_id' => 7,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'conosaki（2026年度カタログ）',
                'description_html' => 'conosakiが大切にしているのは、
子どもたちの毎日に優しく寄り添うランドセル。
「あたたかみのあるやわらかな色」、「洗練された美しいデザイン」、「細部にまでこだわりの詰まったディテール」、「使いやすく安心安全な機能」。素材やシリーズごとのコンセプトを活かした様々なランドセルをラインアップしています。
6年間一緒に過ごす、大切なランドセル。
ご家族みなさんでじっくりと楽しみながら選んでみてください。',
                'note_html' => '2025年2月上旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/conosaki_0107-square-2_200.png',
                'sort_order' => 94,
            ],
            [
                'id' => 41215,
                'maker_id' => 24,
                'brand_id' => 8,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'ふわりぃランドセル2026',
                'description_html' => '「ふわりぃ」は13.5cmマチが標準の軽くて大容量なランドセルです。 荷物がたくさん入るので両手が空き、手ぶらになるので安全に通学することができます。 いつの時代も学校生活に合わせたランドセル開発にいち早く取り組んできた「ふわりぃ」は、お子様一人ひとりの「ランドセルデビュー」を応援しています。
※お届けするカタログは「ふわりぃ（2026年度一般＆フル・イージーオーダーメイドランドセルカタログ）」です。',
                'note_html' => '2025年2月上旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/kyowa_200.jpg',
                'sort_order' => 93,
            ],
            [
                'id' => 41216,
                'maker_id' => 25,
                'brand_id' => 9,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'アタラのランドセル(2026年度カタログ)',
                'description_html' => 'アタラのランドセルは、自然の情景から生まれた優しい色合い、使い勝手の良いミニマルなデザインが特徴です。
ひと目を引くコロンとしたフォルムはタブレットも水筒も、まるっと収納できる見た目以上の大容量タイプ。

両手が荷物でいっぱいにならず、元気に走り回ることができる“ご機嫌”なランドセルです。',
                'note_html' => '2025年2月より順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/atara_soldout_03_200.png',
                'sort_order' => 92,
            ],
            [
                'id' => 41217,
                'maker_id' => 26,
                'brand_id' => 10,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '盛田のランドセル(2026年度カタログ)',
                'description_html' => 'グッドデザイン賞受賞のランドセルは通学時の体の負担を軽減する高機能設計。
さらに軽さと機能性を両立したナイロンランドセル“ラッカル”が新登場！
盛田は創業以来、お子さまの大切な6年間に家族のように寄り添うランドセルをつくっています。',
                'note_html' => '2025年2月下旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'sort_order' => 91,
            ],
            [
                'id' => 41218,
                'maker_id' => 27,
                'brand_id' => 11,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'EISHIN（2026年度カタログ）',
                'description_html' => '「はじまるドキドキ　つづくワクワク」
晴れの入学とともに。そして6年。EISHINのランドセルが、春夏秋冬、大切なお子さんを、守り、支え、いっしょに、すこやかに、歩んでまいります。
デザイン・機能・品質、すべてにこだわった一生モノ。
お子さまが心からときめく、運命の1台を見つけてください！',
                'note_html' => '2025年3月上旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/EISHIN_soldout_200.png',
                'sort_order' => 90,
            ],
            [
                'id' => 41219,
                'maker_id' => 28,
                'brand_id' => 12,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '恐竜ランドセル（2026年度カタログ）',
                'description_html' => '恐竜専門店ダイナソーベースを代表する恐竜ランドセル！
本格的な恐竜骨格がデザインされた恐竜を選び、ランドセルの本体カラー・背中のカラー等を選んで世界に一つだけのオリジナル恐竜ランドセルを作ろう。
その他にも恐竜ランドセルシリーズがあるのでカタログを見てお気に入りを探そう！',
                'note_html' => '順次発送中',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/yamakou_200-1.png',
                'sort_order' => 89,
            ],
            [
                'id' => 41220,
                'maker_id' => 29,
                'brand_id' => 13,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'コクホーのランドセル（2026年度カタログ）',
                'description_html' => '「ずっと好き、がきっと見つかる。」

コクホーのランドセルは幅広いラインナップと充実した機能性が特徴です。
お子様にとって大切な6年間という月日を、
安心して共に歩むことが出来るランドセルをお届けいたします。

【新商品多数】まずはお気軽にカタログをご請求くださいませ。',
                'note_html' => '3月上旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/kokuho_200.jpg',
                'sort_order' => 88,
            ],
            [
                'id' => 41221,
                'maker_id' => 30,
                'brand_id' => 14,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'ごとうじゅうランドセル（2026年度カタログ）',
                'description_html' => '【職人がひとつずつ作り上げる、純国産手作りランドセル工房】

2025年春に新しくスタートするブランド”ごとうじゅうランドセル”は、
お子さまの身体を想ったカスタムメイドのランドセルを中心に、
7シリーズ49バリエーションの多彩なデザインを展開。
職人の熟練の技で丁寧に作り上げる、世界にひとつだけのランドセルをお届けします。',
                'note_html' => '3月上旬～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/gotoju_200.jpg',
                'sort_order' => 87,
            ],
            [
                'id' => 41222,
                'maker_id' => 31,
                'brand_id' => 15,
                'is_display' => false,
                'year' => 2026,
                'display_name' => '地球NASAランドセル®（2026年度カタログ）',
                'description_html' => '『地球の未来は子どもの未来』
最先端テクノロジーとサステナブルな活動でお子さまにも環境にもやさしいランドセルを。
地球NASAランドセル®は、肩ベルトと背あて部分にNASAで採用された衝撃吸収材テンパーフォーム®を搭載。登下校時にかかるお子さまへの負担を軽減する親子で安心の機能性ランドセルです。',
                'note_html' => '2月末～順次発送',
                'mask_image_description' => '受付一時停止中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_soldout_03_200.png',
                'sort_order' => 86,
            ],
        ];
        $this->table('products')->insert($productsData)->save();

        // Insert data for budgets (staging環境用の予算設定)
        $budgetsData = [
            [
                'product_id' => 41211,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41210,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41208,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41209,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 1,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41212,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41213,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 1,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41214,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41215,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41216,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41217,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41218,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41219,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41220,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41221,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 1,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41222,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 99999,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2025-10-31 23:59:59',
                'priority' => 1,
            ],
        ];
        $this->table('budgets')->insert($budgetsData)->save();

        // Insert staging users data
        $hasher = new DefaultPasswordHasher();
        $swbUsersData = [
            [
                'id' => 4210,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('3hEKVL9Glljb-pqB_HW'),
                'last_name' => 'SWB佐藤',
                'first_name' => '亮介',
                'created' => '2025-01-24 17:10:37',
                'modified' => '2025-01-24 17:10:37',
                'deleted' => null,
            ],
            [
                'id' => 4209,
                'authority_id' => 101, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('_yy2LnNFGLvmvt_7gtQ'),
                'last_name' => 'SWB中尾',
                'first_name' => '嘉孝',
                'created' => '2025-01-24 17:07:08',
                'modified' => '2025-04-01 16:25:53',
                'deleted' => null,
            ],
            [
                'id' => 726,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('ECLbe#9LB!UeHSv_W'),
                'last_name' => 'SWB近藤',
                'first_name' => '彪司',
                'created' => '2024-12-04 18:29:35',
                'modified' => '2024-12-04 18:29:35',
                'deleted' => null,
            ],
            [
                'id' => 725,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('fOz!wNRv9Cc3&1jgs'),
                'last_name' => 'SWB石黒',
                'first_name' => '碧',
                'created' => '2024-12-04 18:26:25',
                'modified' => '2024-12-04 18:26:25',
                'deleted' => null,
            ],
            [
                'id' => 724,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('rqnbIJ_0uQeELDOh3'),
                'last_name' => 'SWB佐々木',
                'first_name' => '恵美子',
                'created' => '2024-12-04 18:22:33',
                'modified' => '2024-12-04 18:22:33',
                'deleted' => null,
            ],
            [
                'id' => 519,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => $hasher->hash('cGMKW45#NnZmKDh6V'),
                'last_name' => 'SWB',
                'first_name' => '管理アカウント',
                'created' => '2024-11-25 13:18:09',
                'modified' => '2025-04-01 16:25:28',
                'deleted' => null,
            ],
        ];
        $this->table('swb_users')->insert($swbUsersData)->save();

        $makerUsersData = [
            [
                'id' => 7638,
                'maker_id' => 31,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('VzCVupCN7Djh'),
                'last_name' => '地球',
                'first_name' => 'ランドセル',
                'created' => '2025-03-11 10:42:13',
                'modified' => '2025-03-11 10:42:13',
                'deleted' => null,
            ],
            [
                'id' => 7268,
                'maker_id' => 30,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('22C9Sscjit9F'),
                'last_name' => '後藤重',
                'first_name' => 'ランドセル',
                'created' => '2025-03-03 11:01:28',
                'modified' => '2025-03-03 11:01:28',
                'deleted' => null,
            ],
            [
                'id' => 6473,
                'maker_id' => 29,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('GivUYigz4Has'),
                'last_name' => 'コクホー',
                'first_name' => 'ランドセル',
                'created' => '2025-02-18 17:23:19',
                'modified' => '2025-02-18 17:23:19',
                'deleted' => null,
            ],
            [
                'id' => 4779,
                'maker_id' => 28,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('ATc2ZEK82ZnF'),
                'last_name' => '山耕',
                'first_name' => 'ランドセル',
                'created' => '2025-01-31 10:48:36',
                'modified' => '2025-01-31 10:48:36',
                'deleted' => null,
            ],
            [
                'id' => 4175,
                'maker_id' => 27,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('jFF3ZpQ9dX6j'),
                'last_name' => 'EISHIN',
                'first_name' => 'ランドセル',
                'created' => '2025-01-24 10:36:33',
                'modified' => '2025-01-24 10:36:33',
                'deleted' => null,
            ],
            [
                'id' => 2208,
                'maker_id' => 26,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('uLxWy7m3THHk'),
                'last_name' => '盛田',
                'first_name' => 'ランドセル',
                'created' => '2025-01-07 18:06:57',
                'modified' => '2025-03-17 10:14:17',
                'deleted' => null,
            ],
            [
                'id' => 1315,
                'maker_id' => 25,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('drYN2K8wB4Fr'),
                'last_name' => 'キシル',
                'first_name' => 'ランドセル',
                'created' => '2024-12-24 16:11:30',
                'modified' => '2024-12-24 16:11:30',
                'deleted' => null,
            ],
            [
                'id' => 1306,
                'maker_id' => 24,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('BpyC3uacG4hu'),
                'last_name' => '協和',
                'first_name' => 'ランドセル',
                'created' => '2024-12-24 12:35:33',
                'modified' => '2024-12-24 14:41:50',
                'deleted' => null,
            ],
            [
                'id' => 1125,
                'maker_id' => 23,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('xsA4jKqQZGav'),
                'last_name' => '榮伸',
                'first_name' => 'ランドセル',
                'created' => '2024-12-19 11:30:33',
                'modified' => '2024-12-19 11:30:33',
                'deleted' => null,
            ],
            [
                'id' => 1050,
                'maker_id' => 22,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('VhJVAjw8Wbqg'),
                'last_name' => '戸塚',
                'first_name' => 'ランドセル',
                'created' => '2024-12-17 10:10:00',
                'modified' => '2024-12-17 10:10:00',
                'deleted' => null,
            ],
            [
                'id' => 15,
                'maker_id' => 21,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('JMs5e9pNY3L9'),
                'last_name' => '鞄工房山本',
                'first_name' => 'ランドセル',
                'created' => '2024-10-16 16:16:51',
                'modified' => '2024-10-16 16:16:51',
                'deleted' => null,
            ],
            [
                'id' => 10,
                'maker_id' => 20,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('BxSPgUSWt8nt'),
                'last_name' => 'カバンのフジタ',
                'first_name' => 'ランドセル',
                'created' => '2024-10-08 11:46:45',
                'modified' => '2024-10-11 18:17:33',
                'deleted' => null,
            ],
            [
                'id' => 9,
                'maker_id' => 19,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('wR7MA4UmNS7u'),
                'last_name' => '萬勇鞄',
                'first_name' => 'ランドセル',
                'created' => '2024-10-08 11:43:05',
                'modified' => '2025-01-20 16:24:09',
                'deleted' => null,
            ],
            [
                'id' => 8,
                'maker_id' => 18,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('xwv7N2kq2shS'),
                'last_name' => '羽倉',
                'first_name' => 'ランドセル',
                'created' => '2024-10-08 11:41:00',
                'modified' => '2024-10-11 18:16:47',
                'deleted' => null,
            ],
            [
                'id' => 7,
                'maker_id' => 17,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('r8caAgfrnA3L'),
                'last_name' => '羅羅屋',
                'first_name' => 'ランドセル',
                'created' => '2024-10-08 11:39:01',
                'modified' => '2024-10-11 18:16:06',
                'deleted' => null,
            ],
        ];
        $this->table('maker_users')->insert($makerUsersData)->save();

        $generalUsersData = [
            ['id' => 569, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2024-12-04 00:00:00'],
            ['id' => 1470, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-01-09 00:00:00'],
            ['id' => 3038, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-01-14 00:00:00'],
            ['id' => 3446, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-01-22 00:00:00'],
            ['id' => 4373, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-01-29 00:00:00'],
            ['id' => 5310, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-10 00:00:00'],
            ['id' => 3557, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-19 00:00:00'],
            ['id' => 300, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-21 00:00:00'],
            ['id' => 2040, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-21 00:00:00'],
            ['id' => 5673, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-21 00:00:00'],
            ['id' => 56, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-02-26 00:00:00'],
            ['id' => 423, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-07 00:00:00'],
            ['id' => 6210, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-07 00:00:00'],
            ['id' => 1739, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-07 00:00:00'],
            ['id' => 2502, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 23, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 370, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 1162, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 3349, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 3023, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 4658, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 6956, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 4939, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 3883, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 2750, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 4647, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 6879, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-14 00:00:00'],
            ['id' => 6780, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 3573, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 4649, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 4669, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 2150, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 7403, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-03-19 00:00:00'],
            ['id' => 1703, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-04-01 00:00:00'],
            ['id' => 305, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-04-01 00:00:00'],
            ['id' => 3137, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-04-17 00:00:00'],
            ['id' => 1857, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-01 00:00:00'],
            ['id' => 7840, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-02 00:00:00'],
            ['id' => 4037, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 3740, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 1199, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 1487, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 2582, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 6354, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 4034, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 5900, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 7527, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 6483, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 7558, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 5583, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-07 00:00:00'],
            ['id' => 4365, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 5405, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 1354, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 4084, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 4578, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 3549, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 6763, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 4117, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-15 00:00:00'],
            ['id' => 3694, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-21 00:00:00'],
            ['id' => 310, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-05-28 00:00:00'],
            ['id' => 6270, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-06-03 00:00:00'],
            ['id' => 4793, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-06-03 00:00:00'],
            ['id' => 7843, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-06-05 00:00:00'],
            ['id' => 6743, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-06-10 00:00:00'],
            ['id' => 1701, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-06-16 00:00:00'],
            ['id' => 3300, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-04 00:00:00'],
            ['id' => 4163, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-08 00:00:00'],
            ['id' => 351, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-08 00:00:00'],
            ['id' => 1715, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-16 00:00:00'],
            ['id' => 1597, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-24 00:00:00'],
            ['id' => 2740, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-29 00:00:00'],
            ['id' => 3127, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-07-30 00:00:00'],
            ['id' => 692, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-08-19 00:00:00'],
            ['id' => 2605, 'email' => '<EMAIL>', 'created' => '2024-01-01 00:00:00', 'modified' => '2024-01-01 10:00:00', 'deleted' => '2025-08-19 00:00:00'],
        ];

        $this->table('general_users')->insert($generalUsersData)->save();

        // メンバーのランドセル注文を一般ユーザーに紐づける
        $this->execute('UPDATE randsel_orders
            SET general_user_id = member_id
            WHERE member_id IS NOT NULL;
        ');

        // randsel_ordersのbrand_idを適切な値に更新
        // product_idからbrand_idを取得して更新
        $this->execute('UPDATE randsel_orders ro
            INNER JOIN products p ON ro.product_id = p.id
            SET ro.brand_id = p.brand_id
            WHERE ro.product_id IS NOT NULL
            AND p.brand_id IS NOT NULL
            AND ro.brand_id IS NULL;
        ');

        // Re-enable foreign key checks
        $this->execute('SET FOREIGN_KEY_CHECKS=1');
    }
}
