<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Service\MakersService;
use Cake\TestSuite\TestCase;

/**
 * MakersService Test Case
 */
class MakersServiceTest extends TestCase
{
    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.Makers',
    ];

    /**
     * Test index method
     *
     * @return void
     */
    public function testIndex(): void
    {
        $service = new MakersService();
        $result = $service->index();

        $this->assertIsArray($result);

        if (!empty($result)) {
            $maker = $result[0];
            $this->assertArrayHasKey('maker_id', $maker);
            $this->assertArrayHasKey('maker_name', $maker);
            $this->assertArrayHasKey('ext', $maker);
            
            $ext = $maker['ext'];
            $this->assertArrayHas<PERSON>ey('billing_address', $ext);
            $this->assertArray<PERSON><PERSON><PERSON>ey('customer_code', $ext);
            $this->assertArray<PERSON><PERSON><PERSON><PERSON>('customer_name', $ext);
            $this->assertArray<PERSON>as<PERSON>ey('billing_cycle', $ext);
            $this->assertArrayHas<PERSON>ey('contact_name', $ext);
        }
    }
}
