<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * メーカーエンティティ
 * 
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string|null $maker_image_url
 * @property string|null $maker_features_html
 * @property string|null $other_features_html
 * @property string|null $billing_address
 * @property string|null $customer_code
 * @property string|null $customer_name
 * @property int $billing_cycle
 * @property string|null $contact_name
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\MakerStore[] $maker_stores
 * @property \App\Model\Entity\Brand[] $brands
 * @property \App\Model\Entity\Product[] $products
 * @property \App\Model\Entity\MakerUser[] $maker_users
 * @property \App\Model\Entity\ExhibitionMaker[] $exhibition_makers
 * @property \App\Model\Entity\LiveStreamMaker[] $live_stream_makers
 * @property \App\Model\Entity\RandselOrder[] $randsel_orders
 * @property \App\Model\Entity\RandselInvoice[] $randsel_invoices
 * @property \App\Model\Entity\RandselInvoiceAdjustment[] $randsel_invoice_adjustments
 */
class Maker extends Entity
{
    protected $_accessible = [
        'name' => true,
        'description' => true,
        'maker_image_url' => true,
        'maker_features_html' => true,
        'other_features_html' => true,
        'billing_address' => true,
        'customer_code' => true,
        'customer_name' => true,
        'billing_cycle' => true,
        'contact_name' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'maker_stores' => true,
        'brands' => true,
        'products' => true,
        'maker_users' => true,
        'exhibition_makers' => true,
        'live_stream_makers' => true,
        'randsel_orders' => true,
        'randsel_invoices' => true,
        'randsel_invoice_adjustments' => true,
    ];

    protected $_hidden = [];

    /**
     * 請求サイクル名を取得
     */
    public function getBillingCycleName(): string
    {
        switch ($this->billing_cycle) {
            case 0:
                return '末締め翌月末日払い';
            case 1:
                return '末締め翌々10日';
            case 2:
                return '末締め翌々20日';
            default:
                return '不明';
        }
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブなメーカーかどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }
}
