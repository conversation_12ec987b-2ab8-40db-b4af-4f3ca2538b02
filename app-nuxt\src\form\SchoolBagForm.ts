import { Product } from "~~/src/models/entry/Product";
import { Validations } from "~~/src/lib/Validations";
import { replaceHyphens } from "~~/src/lib/utils";

import {
    getListLabelByValue,
    getLabelsByValues,
    PREFECTURE_LIST,
    SEX_LIST,
    BUDGET_LIST,
    CATALOG_REQUEST_TRIGGER_LIST,
    KEY_POINT_LIST,
} from "~~/src/list";

export default class SchoolBagForm {
    private readonly _products: Product[];
    private readonly _prefectures: TList;
    private readonly _sexes: TList;
    private readonly _budgets: TList;
    private readonly _catalog_request_triggers: TList;
    private readonly _key_points: TList;
    private _valid: boolean = false;
    private _name1: string;
    private _name2: string;
    private _name1_hurigana: string;
    private _name2_hurigana: string;
    private _zip_code: string;
    private _tdfk_cd: string;
    private _address1: string;
    private _address2: string;
    private _address3: string;
    private _tel_1: string;
    private _tel_2: string;
    private _tel_3: string;
    private _email: string;
    private _email_confirmation: string;
    private _login_pwd: string;
    private _login_pwd_confirmation: string;
    private _newsletter_opt_in: boolean;
    private _kiyaku_checked: boolean;
    private _privacy_policy_checked: boolean;

    private _child_name: string;
    private _child_sex: string;
    private _child_birthdate: string;

    private _custom_budget: string;
    private _custom_catalog_request_triggers: string[] = [];
    private _custom_key_points: string[] = [];

    private _question01: string;

    private _order_product_ids: Product[] = [];

    private _errors: { [key: string]: string } = {};

    private _labelkey_map: TJapaneseKeyMap = {
        name1: "姓",
        name2: "名",
        name1_hurigana: "セイ",
        name2_hurigana: "メイ",
        email: "メールアドレス",
        login_pwd: "パスワード",
        newsletter_opt_in: "メルマガの受け取り希望",
        zip_code: "郵便番号",
        tdfk_cd: "都道府県",
        address1: "市区町村・町名",
        address2: "番地（全角）",
        address3: "建物名・部屋番号（全角・任意）",
        tel: "電話番号",
        child_name: "お子さまの名前",
        child_sex: "お子さまの性別",
        child_birthdate: "お子さまの生年月日",
        custom_budget: "ご予算",
        custom_catalog_request_triggers: "カタログ請求のきっかけ（複数回答可）",
        custom_key_points: "特に重視するポイント（複数回答可）",
    };

    constructor(productsData: TProduct[] | []) {
        this._products = Product.creates(productsData);
        this._prefectures = PREFECTURE_LIST; // プルダウン例
        this._sexes = SEX_LIST; // 性別ラジオラベル
        this._budgets = BUDGET_LIST; // 予算ラジオラベル
        this._catalog_request_triggers = CATALOG_REQUEST_TRIGGER_LIST; // きっかけチェックボックスラベル
        this._key_points = KEY_POINT_LIST; // 重視するポイントチェックボックスラベル

        this._order_product_ids = this._products
            .filter(
                (product) =>
                    product.display_type !== "sold_out" &&
                    product.id !== 41219,
            );
            // .map((product) => product.id);
        this._name1 = "";
        this._name2 = "";
        this._name1_hurigana = "";
        this._name2_hurigana = "";
        this._zip_code = "";
        this._tdfk_cd = "";
        this._address1 = "";
        this._address2 = "";
        this._address3 = "";
        this._tel_1 = "";
        this._tel_2 = "";
        this._tel_3 = "";
        this._email = "";
        this._email_confirmation = "";
        this._login_pwd = "";
        this._login_pwd_confirmation = "";
        this._newsletter_opt_in = true;
        this._kiyaku_checked = false;
        this._privacy_policy_checked = false;

        this._child_name = "";
        this._child_sex = "";
        this._child_birthdate = "";

        this._custom_budget = "";
        this._custom_catalog_request_triggers = [];
        this._custom_key_points = [];

        this._question01 = "";
    }

    get prefectures(): TList {
        return this._prefectures;
    }

    get sexes(): TList {
        return this._sexes;
    }

    get budgets(): TList {
        return this._budgets;
    }

    get catalog_request_triggers(): TList {
        return this._catalog_request_triggers;
    }

    get key_points(): TList {
        return this._key_points;
    }

    get order_product_ids(): Product[] {
        return this._order_product_ids;
    }

    set order_product_ids(values: Product[]) {
        this._order_product_ids = values;
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get products(): Product[] {
        return this._products;
    }

    get name1(): string {
        return this._name1;
    }

    set name1(value: string) {
        this._name1 = value;
    }

    get name2(): string {
        return this._name2;
    }

    set name2(value: string) {
        this._name2 = value;
    }

    get name1_hurigana(): string {
        return this._name1_hurigana;
    }

    set name1_hurigana(value: string) {
        this._name1_hurigana = value;
    }

    get name2_hurigana(): string {
        return this._name2_hurigana;
    }

    set name2_hurigana(value: string) {
        this._name2_hurigana = value;
    }

    get name1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name1),
            Validations.maxLength(10, this._labelkey_map.name1),
        ];
    }
    get name2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name2),
            Validations.maxLength(10, this._labelkey_map.name2),
        ];
    }
    get name1_hurigana_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name1_hurigana),
            Validations.maxLength(10, this._labelkey_map.name1_hurigana),
        ];
    }
    get name2_hurigana_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.name2_hurigana),
            Validations.maxLength(10, this._labelkey_map.name2_hurigana),
        ];
    }

    get zip_code(): string {
        return this._zip_code;
    }

    set zip_code(value: string) {
        this._zip_code = value;
    }

    get zip_code_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.zip_code),
            Validations.zipCode(this._labelkey_map.zip_code),
        ];
    }

    get tdfk_cd(): string {
        return this._tdfk_cd;
    }

    set tdfk_cd(value: string) {
        this._tdfk_cd = value;
    }

    get tdfk_cd_rules(): TValidationFunction[] {
        return [Validations.notEmptyString(this._labelkey_map.tdfk_cd)];
    }

    get display_tdfk_cd(): string {
        return getListLabelByValue(this.tdfk_cd, this.prefectures);
    }

    get address1(): string {
        return this._address1;
    }

    set address1(value: string) {
        this._address1 = value;
    }

    get address2(): string {
        return this._address2;
    }

    set address2(value: string) {
        this._address2 = value;
    }

    get address3(): string {
        return this._address3;
    }

    set address3(value: string) {
        this._address3 = value;
    }

    get address1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.address1),
            Validations.maxLength(50, this._labelkey_map.address1),
            Validations.zenkaku(this._labelkey_map.address1),
        ];
    }
    get address2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.address2),
            Validations.maxLength(50, this._labelkey_map.address2),
            Validations.zenkaku(this._labelkey_map.address2),
        ];
    }

    get address3_rules(): TValidationFunction[] {
        return [
            Validations.maxLength(50, this._labelkey_map.address3),
            Validations.zenkaku(this._labelkey_map.address3),
        ];
    }

    get tel_1(): string {
        return this._tel_1;
    }

    set tel_1(value: string) {
        this._tel_1 = value;
    }

    get tel_2(): string {
        return this._tel_2;
    }

    set tel_2(value: string) {
        this._tel_2 = value;
    }

    get tel_3(): string {
        return this._tel_3;
    }

    set tel_3(value: string) {
        this._tel_3 = value;
    }

    get tel1_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}１`),
            Validations.tel1(`${this._labelkey_map.tel}１`),
        ];
    }

    get tel2_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}２`),
            Validations.tel2(`${this._labelkey_map.tel}２`),
        ];
    }

    get tel3_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.tel}３`),
            Validations.tel2(`${this._labelkey_map.tel}３`),
        ];
    }

    get email(): string {
        return this._email;
    }

    set email(value: string) {
        this._email = value;
    }

    get email_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.email),
            Validations.email(this._labelkey_map.email),
        ];
    }

    get email_confirmation(): string {
        return this._email_confirmation;
    }

    set email_confirmation(value: string) {
        this._email_confirmation = value;
    }

    get email_confirmation_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.email}確認用`),
            Validations.email(this._labelkey_map.email),
            Validations.matchedConfirmation(
                this.email,
                this._labelkey_map.email,
                `${this._labelkey_map.email}確認用`,
            ),
        ];
    }

    get login_pwd(): string {
        return this._login_pwd;
    }

    set login_pwd(value: string) {
        this._login_pwd = value;
    }

    get login_pwd_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(this._labelkey_map.login_pwd as string),
            Validations.password(this._labelkey_map.login_pwd as string),
        ];
    }

    get login_pwd_confirmation(): string {
        return this._login_pwd_confirmation;
    }

    set login_pwd_confirmation(value: string) {
        this._login_pwd_confirmation = value;
    }

    get login_pwd_confirmation_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString(`${this._labelkey_map.login_pwd}確認用`),
            Validations.password(`${this._labelkey_map.login_pwd}確認用`),
            Validations.matchedConfirmation(
                this.login_pwd,
                this._labelkey_map.login_pwd as string,
                `${this._labelkey_map.login_pwd}確認用`,
            ),
        ];
    }

    get newsletter_opt_in(): boolean {
        return this._newsletter_opt_in;
    }

    set newsletter_opt_in(value: boolean) {
        this._newsletter_opt_in = value;
    }

    get kiyaku_checked(): boolean {
        return this._kiyaku_checked;
    }

    set kiyaku_checked(value: boolean) {
        this._kiyaku_checked = value;
    }

    get privacy_policy_checked(): boolean {
        return this._privacy_policy_checked;
    }

    set privacy_policy_checked(value: boolean) {
        this._privacy_policy_checked = value;
    }

    get child_name(): string {
        return this._child_name;
    }

    set child_name(value: string) {
        this._child_name = value;
    }

    get child_sex(): string {
        return this._child_sex;
    }

    get display_child_sex(): string {
        return getListLabelByValue(this.child_sex, SEX_LIST);
    }

    set child_sex(value: string) {
        this._child_sex = value;
    }

    get child_birthdate(): string {
        return this._child_birthdate;
    }

    set child_birthdate(value: string) {
        this._child_birthdate = value;
    }

    get custom_budget(): string {
        return this._custom_budget;
    }

    set custom_budget(value: string) {
        this._custom_budget = value;
    }

    get display_custom_budget(): string {
        return getListLabelByValue(this.custom_budget, BUDGET_LIST);
    }

    get custom_catalog_request_triggers(): string[] {
        return this._custom_catalog_request_triggers;
    }

    set custom_catalog_request_triggers(values: string[]) {
        this._custom_catalog_request_triggers = values;
    }

    get display_custom_catalog_request_triggers(): string {
        return getLabelsByValues(
            this.custom_catalog_request_triggers,
            CATALOG_REQUEST_TRIGGER_LIST,
        ).join(",");
    }

    get custom_key_points(): string[] {
        return this._custom_key_points;
    }

    set custom_key_points(values: string[]) {
        this._custom_key_points = values;
    }

    get display_custom_key_points(): string {
        return getLabelsByValues(this.custom_key_points, KEY_POINT_LIST).join(
            ",",
        );
    }

    get labelkey_map(): TJapaneseKeyMap {
        return this._labelkey_map;
    }

    get question01(): string {
        return this._question01;
    }

    set question01(value: string) {
        this._question01 = value;
    }

    get errors(): { [key: string]: string } {
        return this._errors;
    }

    set errors(value: { [key: string]: string }) {
        this._errors = value;
    }

    get tel(): string {
        return `${this.tel_1}-${this.tel_2}-${this.tel_3}`;
    }

    get data(): TSchoolBagFormNewMembersAdd {
        return {
            // name1: this.name1,
            // name2: this.name2,
            // name1_hurigana: this.name1_hurigana,
            // name2_hurigana: this.name2_hurigana,
            // email: this.email,
            // login_pwd: this.login_pwd,
            // zip_code: this.zip_code,
            // tdfk_cd: this.tdfk_cd,
            // address1: this.address1,
            // address2: replaceHyphens(this.address2),
            // address3: replaceHyphens(this.address3),
            // tel: this.tel,
            // email_send_ng_flg: !this.newsletter_opt_in,
            email: this.email,
            password: this.login_pwd, // 暗号化はサーバー側で行う
            profile_data: JSON.stringify({
                last_name: this.name1,
                first_name: this.name2,
                last_name_kana: this.name1_hurigana,
                first_name_kana: this.name2_hurigana,
                zip_code: this.zip_code,
                prefecture_code: this.tdfk_cd,
                address1: this.address1,
                address2: replaceHyphens(this.address2),
                address3: replaceHyphens(this.address3),
                tel: this.tel,
                email_send_ng_flg: !this.newsletter_opt_in,
            }),
            // 商品IDリスト
            product_ids: JSON.stringify(
                this.order_product_ids.map((v: Product) => {
                    return {
                        product_id: v.id,
                        price: v.top_budget.price,
                        type: v.top_budget.type
                    };
                }),
            ),
            // アンケートデータ
            survey_data: JSON.stringify({
                // questions: [
                //     // {
                //     //     k: this._labelkey_map.child_name,
                //     //     v: this.child_name,
                //     // },
                //     {
                //         k: this._labelkey_map.child_sex,
                //         v: getListLabelByValue(this.child_sex, SEX_LIST),
                //     },
                //     {
                //         k: this._labelkey_map.child_birthdate,
                //         v: this.child_birthdate,
                //     },
                //     {
                //         k: this._labelkey_map.custom_budget,
                //         v: getListLabelByValue(this.custom_budget, BUDGET_LIST),
                //     },
                //     {
                //         k: this._labelkey_map.custom_catalog_request_triggers,
                //         v: getLabelsByValues(
                //             this.custom_catalog_request_triggers,
                //             CATALOG_REQUEST_TRIGGER_LIST,
                //         ),
                //     },
                //     {
                //         k: this._labelkey_map.custom_key_points,
                //         v: getLabelsByValues(
                //             this.custom_key_points,
                //             KEY_POINT_LIST,
                //         ),
                //     },
                //     // {
                //     //     k: "入学時期について",
                //     //     // v: this.question02,
                //     // },
                // ],
                year: new Date().getFullYear() + 1, //@todo 年度計算
                child_sex: this.child_sex,
                budget: this.custom_budget,
                // カタログ請求のきっかけを個別のブールフィールドに変換
                question_1_1: this.custom_catalog_request_triggers.includes("1"),
                question_1_2: this.custom_catalog_request_triggers.includes("2"),
                question_1_3: this.custom_catalog_request_triggers.includes("3"),
                question_1_4: this.custom_catalog_request_triggers.includes("4"),
                
                // 重視するポイントを個別のブールフィールドに変換
                question_2_1: this.custom_key_points.includes("1"),
                question_2_2: this.custom_key_points.includes("2"),
                question_2_3: this.custom_key_points.includes("3"),
                question_2_4: this.custom_key_points.includes("4"),
                question_2_5: this.custom_key_points.includes("5"),
                question_2_6: this.custom_key_points.includes("6"),
                question_2_7: this.custom_key_points.includes("7"),
                question_2_8: this.custom_key_points.includes("8"),
                question_2_9: this.custom_key_points.includes("9"),
                question_2_10: this.custom_key_points.includes("10"),
                question_2_11: this.custom_key_points.includes("11"),
                
            }),
            type: "general",
        };
    }

    dataWithJapaneseKeys(): ObjType {
        const data = this.data; // 元のデータを取得
        return Object.keys(data).reduce((acc: ObjType, key) => {
            if (key == "email_send_ng_flg") {
                acc[this._labelkey_map.newsletter_opt_in] =
                    this.newsletter_opt_in;
            } else {
                const japaneseKey =
                    this._labelkey_map[key as keyof TJapaneseKeyMap] || key; // 日本語のキー名を取得、または元のキーを使用
                acc[japaneseKey] =
                    data[key as keyof TSchoolBagFormNewMembersAdd];
            }
            return acc;
        }, {});
    }

    // パスワードをマスクする
    maskPassword(password: string): string {
        return "*".repeat(password.length);
    }

    // エラーメッセージをクリア
    clearError(field: string | string[]): void {
        if (Array.isArray(field)) {
            field.forEach((f) => {
                this.errors[f] = "";
            });
            return;
        }
        this.errors[field] = "";
    }

    customValidate(): boolean {
        Object.keys(this.errors).forEach((key) => {
            this.errors[key] = "";
        });

        this.errors.policy_message = "";
        this.errors.order_product_ids = "";

        if (this.order_product_ids.length < 1) {
            this.errors.order_product_ids =
                "カタログは1つ以上選択してください。";
        }
        if (!(this.kiyaku_checked && this.privacy_policy_checked)) {
            this.errors.policy_message =
                "利用規約およびプライバシーポリシーのご確認をお願いいたします。";
        }

        const hasErrors =
            this.errors.order_product_ids !== "" ||
            this.errors.policy_message !== "";
        return !hasErrors;
    }
}
