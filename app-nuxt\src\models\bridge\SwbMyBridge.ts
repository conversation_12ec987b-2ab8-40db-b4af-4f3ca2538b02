import type { RuntimeConfig } from "nuxt/schema";
import { ClientMyBridge } from "./ClientMyBridge";
import { Maker } from "~~/src/models/entry/Maker";
import { Product } from "~~/src/models/entry/Product";
import { MonthlyInvoice } from "~~/src/models/entry/MonthlyInvoice";
import { MonthlyInvoiceAdjustment } from "~~/src/models/entry/MonthlyInvoiceAdjustment";
import { SwbOrderSearchForm } from "~/src/models/form/SwbOrderSearchForm";
import { SwbInvoiceSearchForm } from "~/src/models/form/SwbInvoiceSearchForm";
import { SwbInvoiceAdjustmentSearchForm } from "~/src/models/form/SwbInvoiceAdjustmentSearchForm";
import { SwbInvoiceAdjustmentForm } from "~/src/models/form/SwbInvoiceAdjustmentForm";
import SwbRandselOrders from "~/src/models/SwbRandselOrders";
import SwbInvoices from "~/src/models/SwbInvoices";
import SwbInvoiceAdjustments from "~/src/models/SwbInvoiceAdjustments";

export class SwbMyBridge extends ClientMyBridge {
    private _makers: Maker[] = [];
    private readonly _swb_order_search_form: SwbOrderSearchForm;
    private readonly _swb_invoice_search_form: SwbInvoiceSearchForm;
    private readonly _swb_invoice_adjustment_search_form: SwbInvoiceAdjustmentSearchForm;
    private _is_invoice_loaded: boolean = true;
    private _is_adjustment_loaded: boolean = true;
    private _swb_monthly_invoices: MonthlyInvoice[] = [];
    private _swb_monthly_invoice_adjustments: MonthlyInvoiceAdjustment[] = [];

    constructor(config: RuntimeConfig, products: Product[]) {
        super(config, products);
        this._swb_order_search_form = new SwbOrderSearchForm();
        this._swb_invoice_search_form = new SwbInvoiceSearchForm();
        this._swb_invoice_adjustment_search_form =
            new SwbInvoiceAdjustmentSearchForm();
    }

    get swb_order_search_form(): SwbOrderSearchForm {
        return this._swb_order_search_form;
    }

    get swb_invoice_search_form(): SwbInvoiceSearchForm {
        return this._swb_invoice_search_form;
    }

    get swb_invoice_adjustment_search_form(): SwbInvoiceAdjustmentSearchForm {
        return this._swb_invoice_adjustment_search_form;
    }

    get is_invoice_loaded(): boolean {
        return this._is_invoice_loaded;
    }

    set is_invoice_loaded(value: boolean) {
        this._is_invoice_loaded = value;
    }

    get is_adjustment_loaded(): boolean {
        return this._is_adjustment_loaded;
    }

    set is_adjustment_loaded(value: boolean) {
        this._is_adjustment_loaded = value;
    }

    get swb_monthly_invoices(): MonthlyInvoice[] {
        return this._swb_monthly_invoices;
    }

    set swb_monthly_invoices(values: MonthlyInvoice[]) {
        this._swb_monthly_invoices = values;
    }

    get swb_monthly_invoice_adjustments(): MonthlyInvoiceAdjustment[] {
        return this._swb_monthly_invoice_adjustments;
    }

    set swb_monthly_invoice_adjustments(values: MonthlyInvoiceAdjustment[]) {
        this._swb_monthly_invoice_adjustments = values;
    }

    get makers(): Maker[] {
        return this._makers;
    }

    set makers(values: Maker[]) {
        this._makers = values;
        this.init();
    }
    init(): void {
        if (!this.products) {
            this.failed();
            return;
        }
        const maker_id = this.makers[this.defaultProductIndex].maker_id;
        this.my_products = this.products.filter((product: Product) => {
            return product.maker_id === maker_id;
        });
        if (this.is_product_loaded) {
            this.loadOrders();
        } else {
            this.failed();
        }
    }
    loadOrders(): void {
        this.is_order_loaded = false;
        if (!this.is_product_loaded) {
            this.failed();
            return;
        }

        // 検索フォームメーカー、カタログ初期化
        if (!this.swb_order_search_form.is_id_set)
            this.swb_order_search_form.productId =
                this.my_products[this.defaultProductIndex].id;
        if (!(this.swb_order_search_form.makerId > 0))
            this.swb_order_search_form.makerId = this.makers[0].maker_id;

        this.swb_order_search_form.resetChangedFlag();

        SwbRandselOrders.create(this._config)
            .index(this.swb_order_search_form.data)
            .then((orders) => {
                this.randsel_orders = orders;
                this.success();
            })
            .catch(() => {
                this.failed();
            });

        // this.client_report_search_form.startDate =
        //     this.swb_order_search_form.startDate;
        // this.client_report_search_form.endDate =
        //     this.swb_order_search_form.endDate;
        // this.client_report_search_form.productId =
        //     this.swb_order_search_form.productId;

        // this.loadReportOrders();
    }

    async loadInvoices(): Promise<void> {
        this.is_invoice_loaded = false;
        if (!this.is_product_loaded) {
            this.failed();
            return;
        }

        this.swb_invoice_search_form.resetChangedFlag();

        try {
            // 承認に基づくデータの取得
            this.swb_monthly_invoices = await SwbInvoices.create(
                this._config,
            ).getMonthlyInvoices(this.swb_invoice_search_form.data);
            this.is_invoice_loaded = true;
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
        } finally {
            this.client_report_search_form.searchDateType = "order-date";
            this.client_report_search_form.resetChangedFlag();
        }
    }

    async loadInvoiceAdjustments(): Promise<void> {
        this.is_adjustment_loaded = false;
        if (!this.is_product_loaded) {
            this.failed();
            return;
        }

        try {
            // 承認に基づくデータの取得
            this.swb_monthly_invoice_adjustments =
                await SwbInvoiceAdjustments.create(
                    this._config,
                ).getMonthlyInvoiceAdjustments(
                    this.swb_invoice_adjustment_search_form.data,
                );
            this.is_adjustment_loaded = true;
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
        } finally {
            this.swb_invoice_adjustment_search_form.resetChangedFlag();
        }
    }

    async addInvoiceAdjustment(item: SwbInvoiceAdjustmentForm): Promise<void> {
        try {
            // 承認に基づくデータの取得
            await SwbInvoiceAdjustments.create(this._config).add(item.data);
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
            alert(
                `追加失敗しました。開発部にご連絡ください。${error}`,
            );
        } finally {
            this.loadInvoiceAdjustments();
        }
    }

    async putInvoiceAdjustment(item: SwbInvoiceAdjustmentForm): Promise<void> {
        try {
            // 承認に基づくデータの取得
            await SwbInvoiceAdjustments.create(this._config).put(item.data);
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
            alert(
                `更新失敗しました。開発部にご連絡ください。${error}`,
            );
        } finally {
            this.loadInvoiceAdjustments();
        }
    }

    async deleteInvoiceAdjustment(id: number): Promise<void> {
        try {
            await SwbInvoiceAdjustments.create(this._config).delete(id);
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
            alert(
                `削除失敗しました。開発部にご連絡ください。${error}`,
            );
        } finally {
            this.loadInvoiceAdjustments();
        }
    }

    async confirmAdjustments(items: SwbInvoiceAdjustmentForm[]): Promise<void> {
        try {
            await SwbInvoiceAdjustments.create(this._config).confirm(
                items.map((item) => item.data as TMonthlyInvoiceAdjustment)
            );
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
            alert(
                `承認失敗しました。開発部にご連絡ください。${error}`,
            );
        } finally {
            this.loadInvoiceAdjustments();
            this.loadInvoices();
        }
    }

    async downloadInvoicePdf(item: MonthlyInvoice): Promise<void> {
        try {
            // 承認に基づくデータの取得
            const blob = await SwbInvoices.create(
                this._config,
            ).downloadInvoicePdf({
                ...item.data,
                ...this.getMakerExtraInfo(item.maker_id),
            } as TMonthlyInvoicesPdf);

            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = `invoice_${
                this.getMakerExtraInfo(item.maker_id).customer_code
            }_${item.status_modified_year_month}.pdf`;
            link.click();
        } catch (error) {
            console.error("Failed to load orders:", error);
            alert(
                `請求データを取得失敗しました。開発部にご連絡ください。${error}`,
            );
            this.failed();
        }
    }

    changeProduct(index: number): void {
        this.my_products = this.products.filter((product: Product) => {
            return product.maker_id === index;
        });
        this.swb_order_search_form.productId =
            this.my_products[this.defaultProductIndex].id;
    }

    getMakerExtraInfo(makerId: number): TMakerExt | Record<string, never> {
        const maker = this.makers.find((maker) => maker.maker_id === makerId);
        return maker ? maker.ext : {};
    }

    failed(): void {
        super.failed();
        this.is_invoice_loaded = true;
        this.is_adjustment_loaded = true;
    }
}
