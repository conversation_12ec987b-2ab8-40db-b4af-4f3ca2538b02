<?php

namespace App\Service;

use App\Enums\EntityFields\ESwbAdjustmentForm;
use App\Kuroko\Entity\Member;
use App\Model\Entity\RandselInvoiceAdjustment;
use App\Model\Table\RandselInvoiceAdjustmentsTable;
use Cake\Log\Log;
use Cake\Utility\Hash;
use Cake\I18n\FrozenTime;
use Exception;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;

class RandselInvoiceAdjustmentsService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselInvoiceAdjustmentsTable::class,
    ];


    public function initialize(): void {}

    /**
     * 調整金額データを返却（1件）
     * @param array $conditions
     * @return RandselInvoiceAdjustment
     */
    public function getAdjustment(array $conditions = []): RandselInvoiceAdjustment
    {
        $adjustment = $this->getDefaultModel()->find('all', ['conditions' => $conditions])->first();

        return $adjustment;
    }

    /**
     * 調整金額データを返却
     * @param array $conditions
     * @param string|null $indexBy (オプション) indexBy を適用するフィールド名。null の場合は適用しない
     * @return array
     */
    public function getAdjustments(array $conditions = [], ?string $indexBy = null): array
    {
        $query = $this->getDefaultModel()->find('all', ['conditions' => $conditions])
        ->contain([
            'RandselInvoiceAdjustmentHistories' => function (Query $q) {
                return $q->orderAsc('RandselInvoiceAdjustmentHistories.id');
            },
            'RandselInvoiceAdjustmentHistories.CreatedByUser',
        ]);

        if ($indexBy !== null) {
            $adjustments = $query->all()->indexBy($indexBy);
        } else {
            $adjustments = $query->all();
        }

        return $adjustments->toArray();
    }

    /**
     * 調整金額データを登録
     * @param array $data
     * @return RandselInvoiceAdjustment
     */
    public function create(array $data): RandselInvoiceAdjustment|null
    {
        try {
            // 調整金額データに履歴データを含める
            $data['histories'] = [
                [
                    'action_type' => 1, // 新規作成
                    'created_by' => $data['created_by'],
                    'changes' => null
                ]
            ];

            $adjustment = $this->getDefaultModel()->newEntity($data, [
                'associated' => ['RandselInvoiceAdjustmentHistories']
            ]);

            if (!($adjustment = $this->getDefaultModel()->save($adjustment))) {
                return null;
            }

            return $adjustment;
        } catch (Exception $e) {
            Log::debug($e->getMessage());
            return null;
        }
    }

    /**
     * 調整金額データ：調整単価、調整数量、コメント、更新者を変更（一般ユーザー用）
     * @param RandselInvoiceAdjustment $entity
     * @return RandselInvoiceAdjustment
     */
    public function updateRandselInvoiceAdjustmentData(RandselInvoiceAdjustment $entity, array $data): RandselInvoiceAdjustment|null
    {
        $changes = [];
        foreach (
            [
                ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE,
                ESwbAdjustmentForm::ADJUSTMENT_QUANTITY,
                ESwbAdjustmentForm::ADJUSTMENT_NOTE,
                ESwbAdjustmentForm::CREATED_BY,
            ] as $item
        ) {
            if (($value = Hash::get($data, $item->value)) !== null) {
                $oldValue = $entity->get($item->value);
                if ($oldValue !== $value && $item->value !== ESwbAdjustmentForm::CREATED_BY->value) {
                    $changes[$item->value] = [
                        'before' => $oldValue,
                        'after' => $value
                    ];
                }
                $entity->set($item->value, $value);
            }
        }

        // ※確定後変更時、is_confirmed: 2 にする
        if ($entity->get(ESwbAdjustmentForm::STATUS->value) === 1) {
            $entity->set(ESwbAdjustmentForm::STATUS->value, 2);
        }

        if (!empty($changes)) {
            $histories = [
                [
                    'action_type' => 2,
                    'created_by' => $data['created_by'],
                    'changes' => $changes
                ]
            ];
            $this->getDefaultModel()->patchEntity($entity, ['histories' => $histories], ['associated' => ['RandselInvoiceAdjustmentHistories']]);
        }

        try {
            if (!($adjustment = $this->getDefaultModel()->save($entity))) {
                return null;
            }
        } catch (Exception $e) {
            Log::debug($e->getMessage());
            return null;
        }
        return $adjustment;
    }

    /**
     * 調整金額データを確定（承認ユーザー用）
     * @param RandselInvoiceAdjustment $entity
     * @return RandselInvoiceAdjustment
     */
    public function confirmRandselInvoiceAdjustmentData(RandselInvoiceAdjustment $entity): RandselInvoiceAdjustment|null
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $memberId = $member->getId();

        // 確認する
        $entity->set(ESwbAdjustmentForm::STATUS->value, 1);
        $entity->set(ESwbAdjustmentForm::CONFIRMED_BY->value, $memberId);
        $entity->set(ESwbAdjustmentForm::CONFIRMED->value, FrozenTime::now()->format('Y-m-d H:i:s'));

        // 履歴データを追加
        $histories = [
            [
                'action_type' => 3, // 確定
                'created_by' => $memberId,
                'changes' => null
            ]
        ];
        
        $this->getDefaultModel()->patchEntity($entity, ['histories' => $histories], [
            'associated' => ['RandselInvoiceAdjustmentHistories']
        ]);

        try {
            if (!($adjustment = $this->getDefaultModel()->save($entity))) {
                return null;
            }
        } catch (Exception $e) {
            Log::debug($e->getMessage());
            return null;
        }
        return $adjustment;
    }

    /**
     * 調整金額データを削除
     * @param string $id
     * @return bool
     */
    public function deleteRandselInvoiceAdjustmentData(string $id): bool
    {
        $adjustment = $this->getAdjustment(['id' => $id]);
        return $this->getDefaultModel()->delete($adjustment);
    }
}
