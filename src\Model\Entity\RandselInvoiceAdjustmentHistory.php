<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * RandselInvoiceAdjustmentHistory Entity
 *
 * @property int $id
 * @property int $invoice_adjustment_id
 * @property int $action_type
 * @property array|null $changes
 * @property int $created_by
 * @property \Cake\I18n\FrozenTime $created
 *
 * @property \App\Model\Entity\RandselInvoiceAdjustment $randsel_invoice_adjustment
 * @property \App\Model\Entity\SwbUser|null $created_by_user
 */
class RandselInvoiceAdjustmentHistory extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'invoice_adjustment_id' => true,
        'action_type' => true,
        'changes' => true,
        'created_by' => true,
        'created' => true,
        'randsel_invoice_adjustment' => true,
    ];

    // 操作種類の定数
    public const ACTION_TYPE_CREATE = 1;
    public const ACTION_TYPE_UPDATE = 2;
    public const ACTION_TYPE_CONFIRM = 3;
}
