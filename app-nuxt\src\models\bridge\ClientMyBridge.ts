import type { RuntimeConfig } from "nuxt/schema";
import { ClientOrderSearchForm } from "~/src/models/form/ClientOrderSearchForm";
import { SwbOrderSearchForm } from "~/src/models/form/SwbOrderSearchForm";
import { ClientReportSearchForm } from "~/src/models/form/ClientReportSearchForm";
import type { Product } from "~~/src/models/entry/Product";
import type { Member } from "~~/src/models/entry/Member";
import type Order from "~/src/models/entry/Order";
import type RandselOrder from "~/src/models/entry/RandselOrder";
import { ClientRandselOrders } from "~/src/models/ClientRandselOrders";

export class ClientMyBridge {
    protected readonly defaultProductIndex: number = 0;

    protected readonly _config: RuntimeConfig;
    protected _client_order_search_form:
        | ClientOrderSearchForm
        | SwbOrderSearchForm;
    private readonly _client_report_search_form: ClientReportSearchForm;
    private readonly _products: Product[] = [];
    protected _my_products: Product[] = [];
    private _member: Member | null = null;
    private _orders: Order[] = [];
    private _randsel_orders: RandselOrder[] = [];
    private _report_orders: RandselOrder[] = [];
    private _report_to: string = "";
    private _report_from: string = "";
    private _is_product_loaded: boolean = false;
    private _is_order_loaded: boolean = false;
    private _is_report_loaded: boolean = false;
    private _is_success: boolean = true;
    constructor(config: RuntimeConfig, products: Product[]) {
        this._config = config;
        this._products = products;
        this._client_order_search_form = new ClientOrderSearchForm();
        this._client_report_search_form = new ClientReportSearchForm();
    }

    get config(): RuntimeConfig {
        return this._config;
    }

    get client_order_search_form(): ClientOrderSearchForm {
        return this._client_order_search_form;
    }

    get client_report_search_form(): ClientReportSearchForm {
        return this._client_report_search_form;
    }

    get products(): Product[] {
        return this._products;
    }

    get my_products(): Product[] {
        return this._my_products;
    }

    get member(): Member | null {
        return this._member;
    }

    get orders(): Order[] {
        return this._orders;
    }

    get randsel_orders(): RandselOrder[] {
        return this._randsel_orders;
    }

    get report_orders(): RandselOrder[] {
        return this._report_orders;
    }

    get report_to(): string {
        return this._report_to;
    }

    get report_from(): string {
        return this._report_from;
    }

    get is_product_loaded(): boolean {
        return this._is_product_loaded;
    }

    get is_order_loaded(): boolean {
        return this._is_order_loaded;
    }

    get is_report_loaded(): boolean {
        return this._is_report_loaded;
    }

    get is_success(): boolean {
        return this._is_success;
    }

    set my_products(values: Product[]) {
        if (values.length) {
            this.is_product_loaded = true;
            this._my_products = values;
        }
    }

    set member(value: Member | null) {
        this._member = value;
        this.init();
    }

    set orders(values: Order[]) {
        this._orders = values;
    }

    set randsel_orders(values: RandselOrder[]) {
        this._randsel_orders = values;
    }

    set report_orders(values: RandselOrder[]) {
        this._report_orders = values;
    }

    set is_product_loaded(value: boolean) {
        this._is_product_loaded = value;
    }

    set is_order_loaded(value: boolean) {
        this._is_order_loaded = value;
    }

    set is_report_loaded(value: boolean) {
        this._is_report_loaded = value;
    }

    set is_success(value: boolean) {
        this._is_success = value;
    }

    init(): void {
        if (!this.member || !this.products) {
            this.failed();
            return;
        }
        const maker_id = this.member.maker_id;
        this.my_products = this.products.filter((product: Product) => {
            return product.maker_id === maker_id;
        });
        if (this.is_product_loaded) {
            this.loadOrders();
            this.loadReportOrders();
        } else {
            this.failed();
        }
    }

    loadOrders(): void {
        this.is_order_loaded = false;
        if (!this.is_product_loaded) {
            this.failed();
            return;
        }
        if (!this.client_order_search_form.is_id_set)
            this.client_order_search_form.productId =
                this.my_products[this.defaultProductIndex].id;
        this.client_order_search_form.resetChangedFlag();

        ClientRandselOrders.create(this._config)
            .index(this.client_order_search_form.data)
            .then((orders) => {
                this.randsel_orders = orders;
                this.success();
            })
            .catch(() => {
                this.failed();
            });
    }

    async loadReportOrders(): Promise<void> {
        this.is_report_loaded = false;
        if (!this.is_product_loaded) {
            this.failed();
            return;
        }
        if (!this.client_report_search_form.is_id_set) {
            this.client_report_search_form.productId =
                this.my_products[this.defaultProductIndex].id;
        }
        this.client_report_search_form.resetChangedFlag();
        try {
            // 注文日に基づくデータの取得
            this.report_orders = await ClientRandselOrders.create(
                this._config,
            ).index({
                ...this.client_report_search_form.data,
                searchDateType: "order-date",
            });
            // 承認日に基づくデータの取得
            const moreOrders = await ClientRandselOrders.create(
                this._config,
            ).index({
                ...this.client_report_search_form.data,
                searchDateType: "status-updated-date",
            });
            // 承認日データにしかないOrderの追加
            const existingIds = new Set(
                this.report_orders.map((order) => order.id),
            );
            moreOrders.forEach((order) => {
                if (!existingIds.has(order.id)) {
                    this.report_orders.push(order);
                }
            });

            this._report_to = this.client_report_search_form.startDate;
            this._report_from = this.client_report_search_form.endDate;
            this.is_report_loaded = true;
        } catch (error) {
            console.error("Failed to load orders:", error);
            this.failed();
        } finally {
            this.client_report_search_form.searchDateType = "order-date";
            this.client_report_search_form.resetChangedFlag();
        }
    }

    success(): void {
        this.is_success = true;
        this.is_product_loaded = true;
        this.is_order_loaded = true;
    }

    failed(): void {
        this.is_success = false;
        this.is_product_loaded = true;
        this.is_order_loaded = true;
        this.is_report_loaded = true;
    }
}
