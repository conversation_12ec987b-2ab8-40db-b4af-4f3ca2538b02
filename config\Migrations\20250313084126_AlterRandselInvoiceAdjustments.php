<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AlterRandselInvoiceAdjustments extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $table = $this->table('randsel_invoice_adjustments');
        $table->changeColumn('is_confirmed', 'integer', [
            'comment' => '状態(0: 未確定, 1: 確定, 2: 確定後変更あり)',
            'default' => 0,
            'limit' => null,
            'null' => false,
        ]);
        $table->update();
        $table->renameColumn('is_confirmed', 'status');
        $table->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $table = $this->table('randsel_invoice_adjustments');
        $table->renameColumn('status', 'is_confirmed');
        $table->update();
        $table->changeColumn('is_confirmed', 'boolean', [
            'comment' => '確定状態(0: 未確定, 1: 確定)',
            'default' => false,
            'limit' => null,
            'null' => true,
        ]);
        $table->update();
    }
}
