export class Product {
    // private readonly _product_id: number;
    // private readonly _product_name: string;
    // private readonly _topics_name: string;
    // private readonly _product_data: TProductData;

    private readonly _id: number;
    private readonly _maker_id: number;
    private readonly _brand_id: number;
    private readonly _display_name: string;
    private readonly _description_html: string;
    private readonly _note_html: string;
    private readonly _mask_image_description: string;
    private readonly _image_url: string;
    private readonly _pdf_url: string;
    private readonly _sort_order: number;
    private readonly _is_display: boolean;
    private readonly _year: number;
    private readonly _brand: object;
    private readonly _maker: object;
    private readonly _top_budget: object;
    private readonly _display_type: "paper" | "digital" | "sold_out";

    constructor(data: TProduct) {
        this._id = data.id;
        this._maker_id = data.maker_id;
        this._brand_id = data.brand_id;
        this._display_name = data.display_name;
        this._description_html = data.description_html;
        this._note_html = data.note_html;
        this._mask_image_description = data.mask_image_description;
        this._image_url = data.image_url;
        this._pdf_url = data.pdf_url;
        this._sort_order = data.sort_order;
        this._is_display = data.is_display;
        this._year = data.year;
        this._brand = data.brand;
        this._maker = data.maker;
        this._top_budget = data.top_budget;
        this._display_type = data.display_type;
    }

    static creates(products: TProduct[]): Product[] {
        return products.map((product: TProduct) => Product.create(product));
    }

    static create(product: TProduct): Product {
        return new Product(product);
    }

    get data(): TProduct {
        return {
            id: this.id,
            maker_id: this.maker_id,
            brand_id: this.brand_id,
            display_name: this.display_name,
            description_html: this.description_html,
            note_html: this.note_html,
            mask_image_description: this.mask_image_description,
            image_url: this.image_url,
            pdf_url: this.pdf_url,
            sort_order: this.sort_order,
            is_display: this.is_display,
            year: this.year,
            brand: this.brand,
            maker: this.maker,
            top_budget: this.top_budget,
            display_type: this.display_type,
        };
    }

    // get mid(): string {
    //     // for (const extColumn of this.product_data.ext_columns.straight) {
    //     //     if (extColumn.ext_slug === "mid") {
    //     //         return extColumn.value;
    //     //     }
    //     // }

    //     const ext = this.product_data.ext;
    //     try {
    //         const mid = JSON.parse(ext);
    //         if (Object.prototype.hasOwnProperty.call(mid, "1")) {
    //             return mid["1"];
    //         }
    //     } catch (error) {
    //         console.error("product_data.ext JSONパース失敗:", error);
    //     }
    //     console.error("No Mid");
    //     return "";
    // }

    // get memo(): string {
    //     const ext = this.product_data.ext;
    //     try {
    //         const extData = JSON.parse(ext);
    //         if (Object.prototype.hasOwnProperty.call(extData, "4")) {
    //             return extData["4"];
    //         }
    //     } catch (error) {
    //         console.error("product_data.ext JSONパース失敗:", error);
    //     }
    //     console.error("No memo");
    //     return "";
    // }

    // get image_url(): string {
    //     const ext = this.product_data.ext;
    //     try {
    //         const extData = JSON.parse(ext);
    //         if (Object.prototype.hasOwnProperty.call(extData, "2")) {
    //             return extData["2"];
    //         }
    //     } catch (error) {
    //         console.error("product_data.ext JSONパース失敗:", error);
    //     }
    //     console.error("No Image");
    //     return "";
    // }

    // get product_data(): TProductData {
    //     return this._product_data;
    // }

    // get product_contents(): string {
    //     return this.product_data.contents;
    // }

    // get product_id(): number {
    //     return this._product_id;
    // }

    // get product_id_string(): string {
    //     return String(this.product_id);
    // }

    // get product_name(): string {
    //     return this._product_name;
    // }

    // get topics_name(): string {
    //     return this._topics_name;
    // }

    get id(): number {
        return this._id;
    }

    get maker_id(): number {
        return this._maker_id;
    }

    get brand_id(): number {
        return this._brand_id;
    }

    get display_name(): string {
        return this._display_name;
    }

    get description_html(): string {
        return this._description_html;
    }

    get note_html(): string {
        return this._note_html;
    }

    get mask_image_description(): string {
        return this._mask_image_description;
    }

    get image_url(): string {
        return this._image_url;
    }

    get pdf_url(): string {
        return this._pdf_url;
    }

    get sort_order(): number {
        return this._sort_order;
    }

    get is_display(): boolean {
        return this._is_display;
    }

    get year(): number {
        return this._year;
    }

    get brand(): object {
        return this._brand;
    }

    get maker(): object {
        return this._maker;
    }

    get top_budget(): object {
        return this._top_budget;
    }

    get display_type(): "paper" | "digital" | "sold_out" {
        return this._display_type;
    }
}
