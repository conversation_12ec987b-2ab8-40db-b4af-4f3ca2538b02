<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AlterRandselOrders extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $table = $this->table('randsel_orders');
        $table->changeColumn('name1', 'text', [
            'comment' => '姓（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('name2', 'text', [
            'comment' => '名（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('name1_hurigana', 'text', [
            'comment' => '姓（ふりがな）（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('name2_hurigana', 'text', [
            'comment' => '名（ふりがな）（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('zip_code', 'text', [
            'comment' => '郵便番号（半角数字7桁）（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('tdfk_cd', 'text', [
            'comment' => '都道府県コード（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('address1', 'text', [
            'comment' => '住所1（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('address2', 'text', [
            'comment' => '住所2（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('address3', 'text', [
            'comment' => '住所3（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('tel', 'text', [
            'comment' => '電話番号（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('email', 'text', [
            'comment' => 'メールアドレス（暗号化） kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('email_send_ng_flg', 'boolean', [
            'comment' => 'メルマガ拒否フラグ (0: 送信可, 1: 送信不可) kuroco→DB後廃棄',
            'default' => false,
            'limit' => null,
            'null' => true,
        ]);
        $table->changeColumn('survey_json', 'text', [
            'comment' => 'アンケートJSON形式 kuroco→DB後廃棄',
            'default' => null,
            'limit' => null,
            'null' => true,
        ]);
        $table->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $table = $this->table('randsel_orders');
        
        // まずNULL値を適切なデフォルト値に更新
        $this->execute('UPDATE randsel_orders SET name1 = "" WHERE name1 IS NULL');
        $this->execute('UPDATE randsel_orders SET name2 = "" WHERE name2 IS NULL');
        $this->execute('UPDATE randsel_orders SET name1_hurigana = "" WHERE name1_hurigana IS NULL');
        $this->execute('UPDATE randsel_orders SET name2_hurigana = "" WHERE name2_hurigana IS NULL');
        $this->execute('UPDATE randsel_orders SET zip_code = "" WHERE zip_code IS NULL');
        $this->execute('UPDATE randsel_orders SET tdfk_cd = "" WHERE tdfk_cd IS NULL');
        $this->execute('UPDATE randsel_orders SET address1 = "" WHERE address1 IS NULL');
        $this->execute('UPDATE randsel_orders SET address2 = "" WHERE address2 IS NULL');
        $this->execute('UPDATE randsel_orders SET tel = "" WHERE tel IS NULL');
        $this->execute('UPDATE randsel_orders SET email = "" WHERE email IS NULL');
        $this->execute('UPDATE randsel_orders SET survey_json = "{}" WHERE survey_json IS NULL');
        
        $table->changeColumn('name1', 'text', [
            'comment' => '姓（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('name2', 'text', [
            'comment' => '名（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('name1_hurigana', 'text', [
            'comment' => '姓（ふりがな）（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('name2_hurigana', 'text', [
            'comment' => '名（ふりがな）（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('zip_code', 'text', [
            'comment' => '郵便番号（半角数字7桁）（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('tdfk_cd', 'text', [
            'comment' => '都道府県コード（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('address1', 'text', [
            'comment' => '住所1（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('address2', 'text', [
            'comment' => '住所2（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('address3', 'text', [
            'comment' => '住所3（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => true,
        ])
        ->changeColumn('tel', 'text', [
            'comment' => '電話番号（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('email', 'text', [
            'comment' => 'メールアドレス（暗号化）',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->changeColumn('email_send_ng_flg', 'boolean', [
            'comment' => 'メルマガ拒否フラグ (0: 送信可, 1: 送信不可)',
            'default' => false,
            'limit' => null,
            'null' => true,
        ])
        ->changeColumn('survey_json', 'text', [
            'comment' => 'アンケートJSON形式',
            'default' => null,
            'limit' => null,
            'null' => false,
        ])
        ->update();
    }
}
