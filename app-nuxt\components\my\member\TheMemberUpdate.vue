<script setup lang="ts">
import { ref } from "vue";
import UserDetails from "~/src/models/UserDetails";
import { useRuntimeConfig } from "#app";
import MemberUpdateForm from "~/src/form/MemberUpdateForm";
import { Member } from "~/src/models/entry/Member";
import ClientZipCodeApiClient from "~/src/lib/http/zip-code/ClientZipCodeApiClient";

const props = defineProps({
    memberUpdateForm: {
        type: Member,
        required: true,
    },
});

const config = useRuntimeConfig();
const emit = defineEmits(["my"]);
// const memberUpdateForm = ref<null | MemberUpdateForm>(null);

const addressHint = [
    "例：○－△－□",
    "※番地がない方は「無番地」をご入力ください。",
];

const tdfkCdField = ref();
const address1Field = ref();

const memberUpdateForm = ref(new MemberUpdateForm(props.memberUpdateForm));
const isLoading = ref(false);

// onMounted(async () => {
//     try {
//         const m = await UserDetails.create(config).index();
//         if (m) {
//             memberUpdateForm.value = reactive(new MemberUpdateForm(m));
//         } else {
//             console.error("member error");
//         }
//     } catch (error) {
//         console.error("Error fetching user details:", error);
//     } finally {
//         isLoading.value = false;
//     }
// });

const toComplete = (): void => {
    Object.keys(memberUpdateForm.value.errors).forEach((key) => {
        memberUpdateForm.value.errors[key] = "";
    });

    if (!memberUpdateForm.value?.valid) {
        return;
    }
    isLoading.value = true;
    UserDetails.create(config)
        .put(memberUpdateForm.value?.data)
        .then((response) => {
            if ("success" in response && response.success) {
                emit("my");
            } else if ("errors" in response) {
                response!.errors.forEach((item) => {
                    memberUpdateForm.value.errors[item.field] = item.message;
                });
            }
            isLoading.value = false;
        });
};

// 郵便番号検索メソッド
const searchZipCode = async (): Promise<void> => {
    const zipCode = memberUpdateForm.value.zip_code;

    // 都道府県と住所１の値とバリデーションチェックをクリアする
    tdfkCdField.value.reset();
    address1Field.value.reset();

    // 郵便番号検索APIを呼び出す
    ClientZipCodeApiClient.create(config)
        .get(zipCode)
        .then((response: TZipCodeApiResponse[]) => {
            if (response.length > 0) {
                memberUpdateForm.value.tdfk_cd = response[0].prefCode;
                memberUpdateForm.value.address1 =
                    response[0].city + response[0].town;
            } else {
                memberUpdateForm.value.errors.zip_code =
                    "住所の検索が失敗しました。";
            }
        })
        .catch((error) => {
            console.error("郵便番号検索エラー:", error);
            memberUpdateForm.value.errors.zip_code =
                "郵便番号の検索に失敗しました。";
        });
};

const gotoMy = (): void => {
    emit("my");
};
</script>

<template>
    <!-- <v-sheet> -->
    <!-- <v-container> -->
    <p class="text-h6 font-weight-bold">登録情報の変更</p>
    <template v-if="isLoading">
        <v-progress-circular indeterminate></v-progress-circular>
    </template>
    <template v-else>
        <template v-if="memberUpdateForm">
            <v-form v-model="memberUpdateForm.valid">
                <v-container class="py-0">
                    <!-- <template v-if="memberUpdateForm.errors.length > 0"> -->
                        <template
                            v-for="(error, key) in memberUpdateForm.errors"
                            :key="key"
                        >
                            <v-alert v-if="error" type="error" >
                                {{ error }}
                            </v-alert>
                        </template>
                    <!-- </template> -->
                    <v-row no-gutters>
                        <v-col cols="12" md="12">
                            <v-label>お名前[漢字]</v-label>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.name1"
                                density="comfortable"
                                :counter="10"
                                :rules="memberUpdateForm.name1_rules"
                                label="姓"
                                required
                            ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.name2"
                                density="comfortable"
                                :counter="10"
                                :rules="memberUpdateForm.name2_rules"
                                label="名"
                                required
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row no-gutters class="mt-2">
                        <v-col cols="12" md="12">
                            <v-label>お名前[フリガナ]</v-label>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.name1_hurigana"
                                density="comfortable"
                                :counter="10"
                                :rules="memberUpdateForm.name1_hurigana_rules"
                                label="セイ"
                                required
                            ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.name2_hurigana"
                                density="comfortable"
                                :counter="10"
                                :rules="memberUpdateForm.name2_hurigana_rules"
                                label="メイ"
                                required
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>

                    <v-row no-gutters class="mt-2">
                        <v-col cols="12" md="12">
                            <v-label>メールアドレス</v-label>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.email"
                                density="comfortable"
                                :counter="50"
                                :rules="memberUpdateForm.email_rules"
                                type="email"
                                :error-messages="memberUpdateForm.errors.email"
                                required
                                @input="memberUpdateForm.clearError('email')"
                            ></v-text-field>
                        </v-col>
                    </v-row>
                    <!-- <div>カバーミーからのメルマガ受け取り希望</div> -->
                    <v-row no-gutters class="mt-2">
                        <v-col cols="12" md="12">
                            <v-label
                                >カバーミーからのメルマガ受け取り希望
                            </v-label>
                            <div class="text-body-2 text-accent">
                                ※新着カタログ情報などをお届けします
                            </div>
                        </v-col>
                    </v-row>
                    <v-row no-gutters>
                        <v-radio-group
                            v-model="memberUpdateForm.newsletter_opt_in"
                            hide-details
                            inline
                        >
                            <v-radio label="希望する" :value="true"></v-radio>
                            <v-radio
                                label="希望しない"
                                :value="false"
                            ></v-radio>
                        </v-radio-group>
                    </v-row>
                    <div class="text-body-2 text-error mt-1 mb-6">
                        ※カバーミーからのメルマガ受け取り希望の選択に関わらず、提携メーカーからメールが送られる可能性がございます
                    </div>
                    <v-row class="mb-2">
                        <v-col cols="12" md="12" class="my-0 py-0">
                            <v-label> 郵便番号（ハイフンなし・半角） </v-label>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.zip_code"
                                density="comfortable"
                                :counter="7"
                                :rules="memberUpdateForm.zip_code_rules"
                                label="例：1234567"
                                :error-messages="
                                    memberUpdateForm.errors.zip_code
                                "
                                single-line
                                required
                                @input="memberUpdateForm.clearError('zip_code')"
                            >
                                <template #append>
                                    <v-btn
                                        variant="flat"
                                        color="primary"
                                        @click="searchZipCode"
                                    >
                                        住所検索
                                    </v-btn>
                                </template>
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row no-gutters class="mt-2">
                        <v-col cols="12" md="12">
                            <v-label>住所（国内住所のみ）</v-label>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-select
                                ref="tdfkCdField"
                                v-model="memberUpdateForm.tdfk_cd"
                                density="comfortable"
                                :items="memberUpdateForm.prefectures"
                                item-title="label"
                                item-value="value"
                                label="都道府県"
                                :rules="memberUpdateForm.tdfk_cd_rules"
                                :error-messages="
                                    memberUpdateForm.errors.tdfk_cd
                                "
                                @input="memberUpdateForm.clearError('tdfk_cd')"
                            ></v-select>
                        </v-col>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                ref="address1Field"
                                v-model="memberUpdateForm.address1"
                                density="comfortable"
                                :counter="50"
                                :rules="memberUpdateForm.address1_rules"
                                label="市区町村・町名"
                                :error-messages="
                                    memberUpdateForm.errors.address1
                                "
                                required
                                hint="例：横浜市西区高島"
                                persistent-hint
                                @input="memberUpdateForm.clearError('address1')"
                            ></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.address2"
                                density="comfortable"
                                :counter="50"
                                :rules="memberUpdateForm.address2_rules"
                                label="番地（全角）"
                                :error-messages="
                                    memberUpdateForm.errors.address2
                                "
                                :messages="addressHint"
                                required
                                persistent-hint
                                @input="memberUpdateForm.clearError('address2')"
                            ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.address3"
                                density="comfortable"
                                :counter="50"
                                :rules="memberUpdateForm.address3_rules"
                                label="建物名・部屋番号（全角）"
                                :error-messages="
                                    memberUpdateForm.errors.address3
                                "
                                hint="例：××ビル １０１"
                                persistent-hint
                                @input="memberUpdateForm.clearError('address3')"
                            ></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row no-gutters class="mt-2">
                        <v-col cols="12" md="12">
                            <v-label>電話番号（半角）</v-label>
                        </v-col>
                    </v-row>
                    <v-row class="flex-nowrap mt-2" no-gutters>
                        <v-col cols="3" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.tel_1"
                                density="comfortable"
                                :rules="memberUpdateForm.tel1_rules"
                                :error-messages="memberUpdateForm.errors.tel"
                                required
                                @input="memberUpdateForm.clearError('tel')"
                            ></v-text-field>
                        </v-col>
                        <div class="mt-5">ー</div>
                        <v-col cols="3" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.tel_2"
                                density="comfortable"
                                :rules="memberUpdateForm.tel2_rules"
                                :error-messages="memberUpdateForm.errors.tel"
                                required
                                @input="memberUpdateForm.clearError('tel')"
                            ></v-text-field>
                        </v-col>
                        <div class="mt-5">ー</div>
                        <v-col cols="3" class="py-1">
                            <v-text-field
                                v-model="memberUpdateForm.tel_3"
                                density="comfortable"
                                :rules="memberUpdateForm.tel3_rules"
                                :error-messages="memberUpdateForm.errors.tel"
                                required
                                @input="memberUpdateForm.clearError('tel')"
                            ></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row justify="center">
                        <v-col cols="12" md="3">
                            <v-btn
                                block
                                color="primary"
                                rounded="xl"
                                size="large"
                                @click="gotoMy"
                            >
                                戻る
                            </v-btn>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-btn
                                block
                                :disabled="!memberUpdateForm.valid"
                                color="primary"
                                rounded="xl"
                                size="large"
                                @click="toComplete"
                            >
                                更新
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-form>
        </template>
        <div v-else>Error loading member details.</div>
    </template>
    <!-- </v-container> -->
    <!-- </v-sheet> -->
</template>

<style scoped></style>
