# coverme プロジェクト

## 環境構築
- docker-compose build
- docker-compose up
- cd .\app-nuxt\
- npm i
- docker exec -it coverme-amazonlinux2-1 bash --login
- cd /opt/coverme
- php composer.phar install

## 最新の更新情報

### 2025-08-18: StagingSeed brand_id更新機能実装
- `randsel_orders`テーブルの`brand_id`カラム自動更新機能を実装
- Product基準とMaker基準の2段階更新ロジック
- 外部キー制約とデータ整合性を考慮した安全な実装
- 詳細: [実装ドキュメント](./docs/seeds/staging-seed-brand-id-update.md)

## ドキュメント

プロジェクトの詳細なドキュメントは [docs/](./docs/) ディレクトリを参照してください。

- [運用手順書](./docs/operations/)
- [実装記録](./docs/seeds/)
- [システム改善提案](./docs/)


# 実装

## Kuroko API の追加

* 事前にkuroko画面側でエンドポイントを追加しておく

- `config/app_kuroko.php`に`endPoint`を追加する
- `src/Kuroko/ApiModel/KurokoApiDynamic/`配下にモデルクラスを作る
    - 同一系統のものは、メソッドを追加する APIのエンドポイントパスにあわせるといい
- `src/Kuroko/Entity`配下に、`src/Kuroko/Entity/IKurokoEntity.php`を実装した返却用Entityを作る
    - `src/Kuroko/Entity/KurokoEntityTrait.php`を`use`する
    - `getJsonData`が、api返却時に返されるフィールドになる
- `app-nuxt/tests/kuroko-check.http`で、APIを直接実行するデータを追記する
    - 本番データのレスポンスjsonを保存する
- `src/Kuroko/Http/Client/Mock`配下に本番レスポンスからjsonファイルを作成して、モックデータにする
- `src/Kuroko/Http/Client/Mock/MockData.php`に、jsonファイルをloadするメソッドを追加する
- `src/Kuroko/Http/Client/KurokoApiMockClient.php`にモックレスポンスを追加する
- `tests/TestCase/Kuroko/ApiModel/KurokoApiModelTest.php`にテストを追加する
    - 別の専用クラス作ってもいい
- モックが返却されることが確認できたらOK
    - `config/app_kuroko.php`の`$useMock`を`!$useMock`にして、unitTestから本番接続確認も可能
- KurokoAPIを操作する具体的な処理は、`src/Service/MembersService.php`のように、専用サービスを設ける

## Cake API の追加

* Frontベース

- `bin/cake bake controller controller_name --prefix Front`でコントローラ作成
    - 名称は複数形になること
    - 実行結果は`config/README.md`にメモ（他コマンド例も書いてある）
    - エラーになるようなら、直接作成してもいい・・
- `src/Controller/FrontController.php`を継承すること
    - メソッドは以下のいずれかをoverrideで実装する(`src/Controller/ApiController.php`にあるメソッドをoverride)
        - index,view,edit,add,delete
- `config/routes.php`にコントローラを追加する
- コントローラとペアになる`Service`クラスを`src/Service`配下に作成する
    - `{名称複数形}Service`となるようにする
    - `src/Service/IRestService.php`を実装する
    - 使わないメソッドは`BadMethodCallException`
    - `protected array $_defaultConfig = []`も必要
    - `src/Service/ServiceTrait.php`を`use`する
- `src/Form/Front`配下に`{コントローラ名}{アクション名,Index,Add,Edit,View}Form`となるフォームクラスを作成
    - `src/Form/ApiForm.php`を継承する
    - パラメータがない場合は`src/Form/DisableParametersForm.php`を使うから不要
- `src/Enums/EntityFields`配下に、フォーム（API,AJAX）で飛んでくるフィールド名を定義するEnumクラスをつくる
    - 既にあるやつを参考にする
- `src/Validation/Validator`配下に、バリデータを作る
    - 既にあるやつを参考にする
    - 流用できるメソッドは、publicにして使いまわしてもいい
- コントローラのアクションとサービスのロジックを実装する
- `tests/TestCase/Controller/Front`配下にテストクラスをつくる
    - `tests/TestCase/Controller/ApiTestCase.php`を継承する
    - 要認証かどうかの調整はすること
        - `$this->setFrontAuthorized()`
        - `$this->setFrontAuthorized(true)`

